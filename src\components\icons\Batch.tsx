import React from 'react';

const MoveIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='24'
    height='24'
    viewBox='0 0 24 24'
    fill='none'
    className={className}
  >
    <path
      d='M17.5 17H18C19.6569 17 21 15.6569 21 14V6C21 4.34315 19.6569 3 18 3H10C8.34315 3 7 4.34315 7 6V6.5'
      strokeWidth='1.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
    <rect
      x='3'
      y='7'
      width='14'
      height='14'
      rx='3'
      stroke-width='1.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
  </svg>
);

export default MoveIcon;
