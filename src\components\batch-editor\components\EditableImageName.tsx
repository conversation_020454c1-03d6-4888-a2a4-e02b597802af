'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Input } from '@/components/ui/Input';
import Image from 'next/image';

interface EditableImageNameProps {
  name: string;
  onNameChange: (newName: string) => void;
  className?: string;
}

/**
 * 可编辑的图片名称组件
 * 支持双击编辑，点击外部或按回车键确认
 */
export function EditableImageName({
  name,
  onNameChange,
  className = '',
}: EditableImageNameProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(name);
  const inputRef = useRef<HTMLInputElement>(null);

  // 当name prop变化时，更新editValue
  useEffect(() => {
    setEditValue(name);
  }, [name]);

  // 开始编辑
  const startEditing = () => {
    setIsEditing(true);
    setEditValue(name);
  };

  // 完成编辑
  const finishEditing = () => {
    setIsEditing(false);
    const trimmedValue = editValue.trim();
    if (trimmedValue && trimmedValue !== name) {
      onNameChange(trimmedValue);
    } else {
      setEditValue(name); // 恢复原值
    }
  };

  // 取消编辑
  const cancelEditing = () => {
    setIsEditing(false);
    setEditValue(name);
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      finishEditing();
    } else if (e.key === 'Escape') {
      cancelEditing();
    }
  };

  // 处理点击外部
  const handleBlur = () => {
    finishEditing();
  };

  // 自动聚焦到输入框
  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, [isEditing]);

  if (isEditing) {
    return (
      <div
        className={`relative group text-[#121212] text-base font-medium truncate cursor-pointer hover:bg-gray-50 rounded transition-colors ${className}`}
      >
        <div className='border border-[#FFCC03] rounded'>
          <Input
            ref={inputRef}
            value={editValue}
            onChange={e => setEditValue(e.target.value)}
            onKeyDown={handleKeyDown}
            onBlur={handleBlur}
            className='text-[#121212] text-base font-medium text-left border-none bg-transparent border-0 py-0.5 px-0.5 h-[22px] focus:ring-0 focus:border-0 w-full shadow-none leading-5'
            style={{ minWidth: '120px' }}
          />
        </div>
      </div>
    );
  }

  return (
    <div
      className={`relative group text-[#121212] text-base font-medium truncate cursor-pointer hover:bg-gray-50 rounded transition-colors ${className}`}
      onDoubleClick={startEditing}
      title='Double click to edit'
    >
      <div className='flex items-center justify-start gap-1'>
        <span className='truncate leading-5'>{name}</span>
        <div
          className='opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex-shrink-0 cursor-pointer hover:bg-gray-100 rounded p-1'
          onClick={e => {
            e.stopPropagation();
            startEditing();
          }}
          title='Click to edit'
        >
          <Image
            src='/apps/icons/edit.svg'
            alt='Edit'
            width={16}
            height={16}
            className='text-[#878787]'
          />
        </div>
      </div>
    </div>
  );
}
