# 多项目部署配置指南

## 架构概述

本指南用于配置多个独立的 Next.js 项目部署在同一域名的不同子路径下。

### 当前部署结构
```
https://pixpretty-test.tenorshare.ai/
├── apps/                      # 当前项目 (AI图像处理)
│   ├── remove-background/     # 去除背景功能
│   ├── batch-editor/          # 批量编辑功能
│   └── api/                   # API 接口
└── [future-projects]/         # 其他未来项目
```

## 当前项目配置

### 🎯 当前方案：basePath 子路径部署

基于运维需求，当前项目已采用 Next.js 的 basePath 配置直接部署到 `/apps` 子路径。

#### 项目配置详情
```typescript
// next.config.ts
const nextConfig: NextConfig = {
  basePath: '/apps',
  assetPrefix: '/apps',
};
```

**部署信息**:
- **仓库**: `ai-image-pixpretty-static`
- **部署路径**: `/apps`
- **主要功能**: 
  - `/apps/remove-background` - AI 去除背景
  - `/apps/batch-editor` - 批量图像编辑
- **API 路径**: `/apps/api/*`

### 路由结构
| 文件路径 | 访问URL | 功能描述 |
|----------|---------|----------|
| `src/app/page.tsx` | `/apps/` | 根页面，重定向到去除背景 |
| `src/app/remove-background/page.tsx` | `/apps/remove-background` | AI 去除背景主功能 |
| `src/app/batch-editor/page.tsx` | `/apps/batch-editor` | 批量编辑功能 |
| `src/app/api/healthz/route.ts` | `/apps/api/healthz` | 健康检查接口 |

## 扩展多项目方案

### 方案对比

| 方案 | 优势 | 劣势 | 适用场景 |
|------|------|------|----------|
| **独立部署 + 反向代理** | 完全独立、易维护、性能好 | 需要配置代理 | 推荐扩展方案 |
| **多 basePath 项目** | 配置简单 | 域名路径冲突 | 小型扩展 |
| **微前端架构** | 运行时整合、共享资源 | 复杂度高 | 大型企业项目 |

### 🚀 推荐扩展方案：独立部署 + 反向代理

当需要添加新项目时，建议使用独立部署配合反向代理的方式。

#### 扩展后的架构
```
https://pixpretty-test.tenorshare.ai/
├── apps/                      # 当前项目 (端口 3001)
│   ├── remove-background/
│   └── batch-editor/
├── tools/                     # 新项目 1 (端口 3002)
│   ├── converter/
│   └── optimizer/
└── admin/                     # 新项目 2 (端口 3003)
    ├── dashboard/
    └── settings/
```

#### 新项目配置示例

##### 工具项目 (Tools)
```typescript
// tools-project/next.config.ts
const nextConfig: NextConfig = {
  basePath: '/tools',
  assetPrefix: '/tools',
};
```

**部署信息**:
- **仓库**: `tools-system` (新仓库)
- **部署端口**: `3002`
- **访问路径**: `/tools/*`

##### 管理项目 (Admin)
```typescript
// admin-project/next.config.ts
const nextConfig: NextConfig = {
  basePath: '/admin',
  assetPrefix: '/admin',
};
```

**部署信息**:
- **仓库**: `admin-system` (新仓库)
- **部署端口**: `3003`
- **访问路径**: `/admin/*`

## Nginx 反向代理配置

### 完整配置示例
```nginx
server {
    listen 80;
    server_name pixpretty-test.tenorshare.ai;

    # 当前 AI 图像处理项目
    location /apps {
        proxy_pass http://localhost:3001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 工具项目 (未来扩展)
    location /tools {
        proxy_pass http://localhost:3002;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 管理项目 (未来扩展)
    location /admin {
        proxy_pass http://localhost:3003;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 根路径重定向到主应用
    location = / {
        return 302 /apps/;
    }

    # 健康检查
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
```

### 🚨 重要说明：nginx location 规则

#### 正确配置
```nginx
# ✅ 推荐配置
location /apps {
    # 匹配：/apps, /apps/, /apps/remove-background, /apps/batch-editor 等
    proxy_pass http://localhost:3001;
}
```

#### 错误配置
```nginx
# ❌ 有问题的配置
location /apps/ {
    # 只匹配：/apps/, /apps/remove-background 等
    # 不匹配：/apps (没有结尾斜杠)
    proxy_pass http://localhost:3001;
}
```

#### 区别说明
- `location /apps` - 匹配所有以 `/apps` 开头的路径
- `location /apps/` - 只匹配以 `/apps/` 开头的路径（必须有结尾斜杠）

当用户访问 `https://domain.com/apps` 时：
- 第一种配置 ✅ 正常工作
- 第二种配置 ❌ 返回 404 错误

## 🚀 部署流程

### 1. 当前项目部署
```bash
# 在当前项目目录
npm run build
pm2 start ecosystem.config.js --name "ai-image-app" --env production
```

### 2. 新项目创建与部署流程

#### 创建新项目
```bash
# 创建工具项目
npx create-next-app@latest tools-system --typescript --tailwind --eslint
cd tools-system

# 配置 basePath
echo 'const nextConfig = { basePath: "/tools", assetPrefix: "/tools" };
export default nextConfig;' > next.config.ts

# 开发新项目功能
# ... 

# 构建和部署
npm run build
pm2 start ecosystem.config.js --name "tools-app" --env production
```

### 3. PM2 配置示例

#### 当前项目 ecosystem.config.js
```javascript
module.exports = {
  apps: [{
    name: 'ai-image-app',
    script: 'node_modules/next/dist/bin/next',
    args: 'start -p 3001',
    cwd: '/path/to/ai-image-pixpretty-static',
    env: {
      NODE_ENV: 'production'
    }
  }]
};
```

#### 新项目 ecosystem.config.js 示例
```javascript
module.exports = {
  apps: [{
    name: 'tools-app',
    script: 'node_modules/next/dist/bin/next',
    args: 'start -p 3002',
    cwd: '/path/to/tools-system',
    env: {
      NODE_ENV: 'production'
    }
  }]
};
```

## 🔧 Docker 部署方案（可选）

### docker-compose.yml
```yaml
version: '3.8'

services:
  ai-image-app:
    build: ./ai-image-pixpretty-static
    ports:
      - "3001:3000"
    environment:
      - NODE_ENV=production
    restart: unless-stopped

  tools-app:
    build: ./tools-system
    ports:
      - "3002:3000"
    environment:
      - NODE_ENV=production
    restart: unless-stopped

  admin-app:
    build: ./admin-system
    ports:
      - "3003:3000"
    environment:
      - NODE_ENV=production
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - ai-image-app
      - tools-app
      - admin-app
    restart: unless-stopped
```

## 📋 新项目开发清单

### 1. 项目初始化
- [ ] 创建新的 Git 仓库
- [ ] 初始化 Next.js 项目
- [ ] 配置对应的 basePath（如 `/tools`, `/admin`）
- [ ] 设置 TypeScript 和 Tailwind CSS
- [ ] 配置 ESLint 和 Prettier

### 2. 路由设计示例
```typescript
// 工具项目的路由结构
src/app/
├── page.tsx              # /tools/ - 工具主页
├── converter/
│   ├── page.tsx         # /tools/converter - 格式转换
│   └── [type]/
│       └── page.tsx     # /tools/converter/[type] - 具体转换
├── optimizer/
│   └── page.tsx         # /tools/optimizer - 图像优化
└── api/
    └── convert/
        └── route.ts     # /tools/api/convert - 转换 API
```

### 3. 部署配置
- [ ] 配置生产环境端口（如 3002, 3003）
- [ ] 设置 PM2 或 Docker 配置
- [ ] 更新 Nginx 配置添加新路由规则
- [ ] 测试新项目的独立访问

### 4. 共享资源管理
```typescript
// 创建共享的组件库或工具库
// 通过 npm 私有包或 Git 子模块共享
import { CommonLayout } from '@company/shared-ui';
import { imageUtils } from '@company/shared-utils';
```

## 🔍 测试验证

### 当前项目测试
```bash
# 本地开发测试
npm run dev

# 访问测试
curl http://localhost:3000/apps/
curl http://localhost:3000/apps/remove-background
curl http://localhost:3000/apps/batch-editor
```

### 生产环境测试
```bash
# 访问路径验证
https://pixpretty-test.tenorshare.ai/apps/
https://pixpretty-test.tenorshare.ai/apps/remove-background
https://pixpretty-test.tenorshare.ai/apps/batch-editor
```

### 多项目扩展测试
```bash
# 新项目测试 (未来)
https://pixpretty-test.tenorshare.ai/tools/
https://pixpretty-test.tenorshare.ai/admin/
```

## 🎯 最佳实践

1. **basePath 规范**: 每个项目使用独立的 basePath，避免路径冲突
2. **独立开发**: 每个项目维护独立仓库和开发周期
3. **统一技术栈**: 使用相同的 Next.js 版本和开发规范
4. **共享资源**: 通过 npm 包或 monorepo 共享通用组件
5. **监控部署**: 使用 PM2 或 Docker 进行进程管理和监控
6. **反向代理**: 通过 Nginx 统一管理多项目路由
7. **健康检查**: 为每个项目配置健康检查接口

## 当前状态总结

✅ **当前项目**: 已使用 basePath 配置完成 `/apps` 路径部署  
✅ **功能完整**: 去除背景和批量编辑功能正常运行  
✅ **部署就绪**: 适配运维的子路径部署需求  
🔄 **扩展准备**: 为未来多项目扩展预留了架构空间 