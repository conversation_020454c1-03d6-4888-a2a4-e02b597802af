import Cookies from 'js-cookie';

/**
 * 从 Cookie 中获取 access_token
 */
export function getAccessToken() {
  return Cookies.get('access_token');
}

/**
 * 把 accessToken 存储到 Cookie, 15 天后失效
 * @param accessToken
 */
export function setAccessToken(accessToken: string) {
  Cookies.set('access_token', accessToken, {
    domain: '.tenorshare.ai',
    path: '/', // 在整个网站上有效
    expires: 15,
  });
}

export function setRedirectHref(redirectHref: string) {
  Cookies.set(`redirect_href`, redirectHref, {
    domain: '.tenorshare.ai',
    path: '/', // 在整个网站上有效
    expires: 15,
  });
}
/**
 * 将 Cookie 中的 accessToken 移除
 */
export function removeAccessToken() {
  return Cookies.remove(`access_token`, {
    domain: '.tenorshare.ai',
    path: '/', // 在整个网站上有效
  });
}

export function removeRedirectHref() {
  return Cookies.remove(`redirect_href`, {
    domain: '.tenorshare.ai',
    path: '/', // 在整个网站上有效
  });
}
