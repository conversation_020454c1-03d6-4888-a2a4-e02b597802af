# AI 背景去除工具

企业级 AI 图片处理应用，集成专业背景去除、图片编辑和批量处理功能。

## ✨ 核心功能

- 🤖 **AI 背景去除** - 集成 Photoroom API 的专业级背景移除
- 🎨 **实时编辑** - Canvas 图片编辑器，支持缩放、旋转、拖拽
- � **多设备支持** - 桌面端和移动端专门优化的界面
- 🔄 **批量处理** - 支持多图片同时处理和批量下载
- �️ **企业认证** - 集成外部登录中心的强制认证系统
- ⚡ **高性能** - 统一图片处理管道，支持撤销/重做

## 🛠️ 技术栈

- **Next.js 15** - App Router + 中间件认证
- **TypeScript** - 完整类型安全
- **Tailwind CSS v4** - 现代化样式系统
- **Zustand** - 轻量级状态管理
- **shadcn/ui** - 高质量组件库

## 🚀 快速开始

### 1. 安装依赖
```bash
npm install
```

### 2. 环境配置
创建 `.env.local` 文件：
```bash
# API 配置
NEXT_PUBLIC_API_BASE_URL="https://api-test.tenorshare.ai/pixpretty"
PHOTOROOM_API_KEY="your_photoroom_api_key"

# 认证系统
NEXT_PUBLIC_LOGIN_URL="https://account-test.tenorshare.ai/#/login"
NEXT_PUBLIC_ACCOUNT_URL="https://account-test.tenorshare.ai"
```

### 3. 启动开发
```bash
npm run dev
```

访问 [http://localhost:3000/apps](http://localhost:3000/apps) 开始使用。

## 📁 项目架构

```
src/
├── app/                    # Next.js App Router
│   ├── remove-background/  # 背景去除页面
│   ├── batch-editor/       # 批量编辑页面
│   └── api/               # API 路由
├── components/
│   ├── auth/              # 认证系统
│   ├── background-remover/ # 桌面端组件
│   ├── mobile-background-remover/ # 移动端组件
│   ├── batch-editor/      # 批量编辑组件
│   └── ui/               # 基础 UI 组件
├── lib/                   # 工具库
│   ├── imageUtils/        # 图片处理工具
│   └── authUtils.ts       # 认证工具
├── store/                 # 状态管理
└── middleware.ts          # 认证中间件
```

## 🔧 主要功能

### 认证系统
- 强制登录，集成外部登录中心
- 中间件级别的路由保护
- 自动 Token 管理和重定向

### 图片处理
- AI 背景去除（Photoroom API）
- Canvas 实时编辑器
- 批量处理和下载
- 统一处理管道

### 多设备支持
- 智能设备检测
- 桌面端完整功能界面
- 移动端触摸优化界面

## 📚 文档

详细文档请查看 [docs/文档中心.md](./docs/文档中心.md)

## 🚀 部署

项目配置了 `basePath: '/apps'`，部署到 `/apps` 子路径下。

生产环境访问地址：`https://pixpretty-test.tenorshare.ai/apps/`

