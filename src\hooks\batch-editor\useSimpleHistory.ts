'use client';

import { useCallback, useState, useEffect } from 'react';
import { type TemporalState } from 'zundo';
import {
  useImageStore,
  undo,
  redo,
  clearHistory,
  type StoreState,
} from '@/store/imageStore';

/**
 * 简化的历史记录 Hook
 * 直接使用 imageStore 的 temporal 中间件，避免复杂的自定义历史记录系统
 */
export const useSimpleHistory = () => {
  // 使用状态来跟踪 temporal 状态
  const [temporalState, setTemporalState] = useState(() => {
    return useImageStore.temporal?.getState();
  });

  // 订阅 temporal 状态变化
  useEffect(() => {
    const temporal = useImageStore.temporal;
    if (!temporal) return;

    // 订阅 temporal 状态变化
    const unsubscribe = temporal.subscribe(
      (state: TemporalState<Pick<StoreState, 'images'>>) => {
        setTemporalState(state);
      }
    );

    return unsubscribe;
  }, []);

  // 计算是否可以撤销/重做
  const canUndo = temporalState?.pastStates?.length > 0;
  const canRedo = temporalState?.futureStates?.length > 0;

  // 更新 temporal 状态的函数（用于手动触发更新）
  const updateTemporalState = useCallback(() => {
    const newState = useImageStore.temporal?.getState();
    setTemporalState(newState);
  }, []);

  // UI 更新回调集合
  const [uiUpdateCallbacks] = useState(() => new Set<() => void>());

  // 通知所有注册的 UI 更新回调
  const notifyUIUpdate = useCallback(() => {
    uiUpdateCallbacks.forEach(callback => {
      try {
        callback();
      } catch (error) {
        console.error('🔄 [useSimpleHistory] UI 更新回调执行失败:', error);
      }
    });
  }, [uiUpdateCallbacks]);

  // 注册 UI 更新回调
  const registerUIUpdateCallback = useCallback(
    (callback: () => void) => {
      uiUpdateCallbacks.add(callback);
      return () => {
        uiUpdateCallbacks.delete(callback);
      };
    },
    [uiUpdateCallbacks]
  );

  // 撤销操作
  const handleUndo = useCallback(() => {
    if (!canUndo) {
      return;
    }

    try {
      undo(); // 使用导出的 undo 函数

      // 延迟通知 UI 更新，确保状态已经更新
      setTimeout(() => {
        updateTemporalState(); // 更新 temporal 状态
        notifyUIUpdate();
      }, 50);
    } catch (error) {
      console.error('🔄 [useSimpleHistory] 撤销操作失败:', error);
    }
  }, [canUndo, updateTemporalState, notifyUIUpdate]);

  // 重做操作
  const handleRedo = useCallback(() => {
    if (!canRedo) {
      return;
    }

    try {
      redo(); // 使用导出的 redo 函数

      // 延迟通知 UI 更新，确保状态已经更新
      setTimeout(() => {
        updateTemporalState(); // 更新 temporal 状态
        notifyUIUpdate();
      }, 50);
    } catch (error) {
      console.error('🔄 [useSimpleHistory] 重做操作失败:', error);
    }
  }, [canRedo, updateTemporalState, notifyUIUpdate]);

  // 清空历史记录
  const handleClearHistory = useCallback(() => {
    try {
      clearHistory(); // 使用导出的 clearHistory 函数
      updateTemporalState(); // 更新 temporal 状态
      notifyUIUpdate();
    } catch (error) {
      console.error('🔄 [useSimpleHistory] 清空历史记录失败:', error);
    }
  }, [updateTemporalState, notifyUIUpdate]);

  // 批量操作：暂停历史记录，执行操作，然后恢复
  const performBatchOperation = useCallback(
    async (operation: () => Promise<void> | void) => {
      // 暂停历史记录跟踪
      useImageStore.temporal.getState().pause();

      try {
        await operation();
      } catch (error) {
        console.error('🔄 [useSimpleHistory] 批量操作失败:', error);
        throw error;
      } finally {
        // 恢复历史记录跟踪
        useImageStore.temporal.getState().resume();

        // 通知 UI 更新
        setTimeout(() => {
          notifyUIUpdate();
        }, 50);
      }
    },
    [notifyUIUpdate]
  );

  // 获取历史记录信息
  const getHistoryInfo = useCallback(() => {
    const currentTemporalState = useImageStore.temporal?.getState();
    return {
      canUndo,
      canRedo,
      pastStatesCount: currentTemporalState?.pastStates?.length || 0,
      futureStatesCount: currentTemporalState?.futureStates?.length || 0,
    };
  }, [canUndo, canRedo]);

  return {
    // 状态
    canUndo,
    canRedo,

    // 核心操作
    undo: handleUndo,
    redo: handleRedo,
    clearHistory: handleClearHistory,

    // 批量操作
    performBatchOperation,

    // UI 更新管理
    registerUIUpdateCallback,
    notifyUIUpdate,

    // 信息查询
    getHistoryInfo,
  };
};

/**
 * 导出便捷的历史记录操作函数
 */
export const simpleHistoryActions = {
  undo: () => {
    const temporalState = useImageStore.temporal?.getState();
    if (temporalState?.pastStates?.length > 0) {
      temporalState.undo();
    }
  },

  redo: () => {
    const temporalState = useImageStore.temporal?.getState();
    if (temporalState?.futureStates?.length > 0) {
      temporalState.redo();
    }
  },

  clear: () => {
    const temporalState = useImageStore.temporal?.getState();
    if (temporalState) {
      temporalState.clear();
    }
  },

  canUndo: () => {
    const temporalState = useImageStore.temporal?.getState();
    return temporalState?.pastStates?.length > 0;
  },

  canRedo: () => {
    const temporalState = useImageStore.temporal?.getState();
    return temporalState?.futureStates?.length > 0;
  },
};
