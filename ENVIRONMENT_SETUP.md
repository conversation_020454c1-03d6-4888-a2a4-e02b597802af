# 环境变量配置指南

## 🔧 必需的环境变量

### PHOTOROOM_API_KEY
**必需** - Photoroom API 密钥

#### 获取步骤：
1. 访问 [Photoroom API](https://www.photoroom.com/api/)
2. 注册账户并获取 API 密钥
3. 复制您的 API 密钥

#### 配置方法：

**开发环境：**
在项目根目录创建 `.env.local` 文件：
```bash
PHOTOROOM_API_KEY=your_actual_api_key_here
```

**生产环境 (Vercel)：**
1. 进入 Vercel 项目设置
2. 找到 "Environment Variables" 部分
3. 添加：
   - Name: `PHOTOROOM_API_KEY`
   - Value: `your_production_api_key`

**生产环境 (其他平台)：**
根据您的部署平台设置环境变量。

## 🚨 重要提醒

- ❗ **没有配置 API 密钥，应用将无法正常工作**
- 🔐 **不要将真实 API 密钥提交到版本控制**
- 📝 **`.env.local` 文件已被 `.gitignore` 忽略**
- 🔄 **修改环境变量后需要重启开发服务器**

## 🧪 测试配置

创建 `.env.local` 文件后，运行：
```bash
npm run dev
```

然后上传一张图片测试背景移除功能。

## 📋 配置检查清单

- [ ] 已创建 `.env.local` 文件
- [ ] 已添加 `PHOTOROOM_API_KEY` 变量
- [ ] API 密钥格式正确（以 `pk_` 或 `sk_` 开头）
- [ ] 重启了开发服务器
- [ ] 测试了图片上传功能

## 🛠️ 故障排除

### 错误："Photoroom API密钥未配置"
**解决方案：**
1. 确认 `.env.local` 文件在项目根目录
2. 确认变量名为 `PHOTOROOM_API_KEY`
3. 确认API密钥正确且有效
4. 重启开发服务器：`npm run dev`

### 错误："400 Bad Request"
**解决方案：**
1. 检查 API 密钥是否有效
2. 确认图片格式支持（JPEG, PNG, WebP）
3. 检查图片大小是否合理（建议 < 10MB）
4. 查看浏览器控制台的详细错误信息

### 错误："API 调用失败"
**解决方案：**
1. 检查网络连接
2. 验证 API 配额是否用完
3. 确认 Photoroom 服务状态
4. 检查防火墙设置

## 📞 获取帮助

如果遇到配置问题：
1. 查看浏览器控制台错误信息
2. 检查网络选项卡中的 API 请求
3. 参考 [Photoroom API 文档](https://docs.photoroom.com/)
4. 联系 Photoroom 技术支持 