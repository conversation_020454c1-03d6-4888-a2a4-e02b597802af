import fs from 'fs/promises';
import { NextResponse } from 'next/server';
import os from 'os';

// 简单的内存存储用于统计（生产环境建议使用 Redis 或数据库）
const requestStats = {
  totalRequests: 0,
  healthChecks: 0,
  errors: 0,
  lastRequestTime: new Date().toISOString(),
  requestsPerMinute: 0,
  startTime: new Date(),
};

// 格式化运行时间为可读格式
const formatUptime = (seconds: number): string => {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  return `${days}天 ${hours}小时 ${minutes}分钟 ${secs}秒`;
};

// 获取文件系统信息
const getFilesystemInfo = async () => {
  try {
    const stats = await fs.stat('.');
    return {
      accessible: true,
      lastModified: stats.mtime.toISOString(),
      // 在生产环境中可以添加磁盘使用情况等信息
    };
  } catch (error) {
    return {
      accessible: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
};

// 请求计数器
const incrementStats = () => {
  requestStats.totalRequests++;
  requestStats.healthChecks++;
  requestStats.lastRequestTime = new Date().toISOString();

  // 计算每分钟请求数（简化版）
  const minutesRunning =
    (Date.now() - requestStats.startTime.getTime()) / (1000 * 60);
  requestStats.requestsPerMinute = Math.round(
    requestStats.totalRequests / (minutesRunning || 1)
  );
};

export async function GET() {
  try {
    incrementStats();

    // 获取系统详细信息
    const memUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();

    const healthCheck = {
      status: 'ok',
      timestamp: new Date().toISOString(),

      // 🕐 系统运行时间信息
      uptime: {
        seconds: Math.floor(process.uptime()),
        human: formatUptime(process.uptime()),
        startTime: requestStats.startTime.toISOString(),
      },

      // 💾 内存使用详情
      memory: {
        used: Math.round(memUsage.heapUsed / 1024 / 1024),
        total: Math.round(memUsage.heapTotal / 1024 / 1024),
        external: Math.round(memUsage.external / 1024 / 1024),
        rss: Math.round(memUsage.rss / 1024 / 1024),
        usage: Math.round((memUsage.heapUsed / memUsage.heapTotal) * 100),
      },

      // 🖥️ CPU 信息
      cpu: {
        user: Math.round(cpuUsage.user / 1000), // 微秒转毫秒
        system: Math.round(cpuUsage.system / 1000),
        loadAverage:
          process.platform !== 'win32' ? os.loadavg() : 'N/A (Windows)',
        cores: os.cpus().length,
      },

      // 📊 请求统计
      requests: {
        total: requestStats.totalRequests,
        healthChecks: requestStats.healthChecks,
        errors: requestStats.errors,
        perMinute: requestStats.requestsPerMinute,
        lastRequest: requestStats.lastRequestTime,
      },

      // 🌐 系统环境
      environment: {
        nodeEnv: process.env.NODE_ENV || 'unknown',
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
        pid: process.pid,
        hostname: os.hostname(),
      },

      // 📁 文件系统信息
      filesystem: await getFilesystemInfo(),

      // 🌟 应用特定信息
      application: {
        name: 'AI Image PixPretty',
        version: process.env.npm_package_version || '1.0.0',
        features: ['background-removal', 'image-editing', 'api'],
      },
    };

    return NextResponse.json(healthCheck, { status: 200 });
  } catch (error) {
    requestStats.errors++;
    console.error('Health check failed:', error);

    return NextResponse.json(
      {
        status: 'error',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 503 } // Service Unavailable
    );
  }
}
