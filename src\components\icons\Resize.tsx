import React from 'react';

interface IconProps {
  className?: string;
  size?: number;
}

const ResizeIcon: React.FC<IconProps> = ({ className, size = 24 }) => (
  <svg
    width={size}
    height={size}
    viewBox='0 0 24 24'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    className={className}
  >
    <rect
      x='3'
      y='3'
      width='18'
      height='18'
      rx='4'
      strokeWidth='1.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
    <path
      d='M3 8H13C14.6569 8 16 9.34315 16 11V21'
      strokeWidth='1.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
    <path
      d='M3 13H9C10.1046 13 11 13.8954 11 15V21'
      strokeWidth='1.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
  </svg>
);

export default ResizeIcon;
