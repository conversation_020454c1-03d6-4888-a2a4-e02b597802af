# 认证系统使用指南

## 概述

本项目已集成简化的认证系统，实现了以下功能：
- 强制登录（无游客模式）
- 路由保护
- 用户状态管理
- 外部登录中心集成

## 核心组件

### 1. 认证状态管理 (`src/store/accountStore.ts`)

使用Zustand管理全局认证状态：

```typescript
import { useAuthStore } from '@/store/accountStore';

function MyComponent() {
  const { 
    isAuthenticated, 
    userInfo, 
    login, 
    logout, 
    checkAuth 
  } = useAuthStore();
  
  // 使用认证状态...
}
```

### 2. 认证守卫 (`src/components/auth/AuthGuard.tsx`)

保护需要登录的组件：

```typescript
import { AuthGuard } from '@/components/auth';

function ProtectedComponent() {
  return (
    <AuthGuard>
      <div>只有登录用户才能看到这里</div>
    </AuthGuard>
  );
}
```

### 3. 认证提供者 (`src/components/auth/AuthProvider.tsx`)

在根布局中提供认证上下文，已集成在 `src/app/layout.tsx` 中。

### 4. 用户信息组件 (`src/components/auth/UserProfile.tsx`)

显示用户信息和登出按钮：

```typescript
import { UserProfile } from '@/components/auth';

function Header() {
  return (
    <div className="header">
      <UserProfile variant="compact" />
    </div>
  );
}
```

## 认证流程

### 1. 用户访问应用

1. 中间件检查cookie中的token
2. 如果没有token，重定向到外部登录中心
3. 如果有token，继续访问

### 2. 登录流程

1. 用户访问应用任意路由
2. 系统检查cookie中的token
3. 如果没有token，自动重定向到外部登录中心
4. 登录成功后，外部系统在本地种植cookie
5. 通过回调地址返回到原始页面

### 3. Token管理

- Token由外部登录中心管理并种植到cookie中
- 系统只读取cookie中的token
- Token失效时自动重定向到外部登录中心

## 环境变量配置

确保以下环境变量已正确配置：

```bash
# API基础URL
NEXT_PUBLIC_API_BASE_URL="https://api-test.tenorshare.ai/pixpretty"

# 外部登录系统URL
NEXT_PUBLIC_LOGIN_URL="https://account-test.tenorshare.ai/#/login"

# 注册页面URL
NEXT_PUBLIC_REGISTER_URL="https://account-test.tenorshare.ai/#/signUp"

# 账户管理URL
NEXT_PUBLIC_ACCOUNT_URL="https://account-test.tenorshare.ai"
```

## 路由配置

### 受保护路由（需要认证）
- 所有路由都需要登录

### 中间件配置

`middleware.ts` 实现了路由级别的保护：
- 检查cookie中的token
- 自动重定向未认证用户到外部登录中心

## API集成

### HTTP客户端

`src/api/http-client.ts` 自动处理：
- 请求头中添加token
- Token失效时自动登出
- 错误处理和重试

### 认证API

`src/api/auth.ts` 提供：
- 用户信息获取
- Token刷新
- 登出接口

## 使用示例

### 1. 检查认证状态

```typescript
import { useAuthCheck } from '@/components/auth';

function MyComponent() {
  const { isAuthenticated, isLoading } = useAuthCheck();
  
  if (isLoading) return <div>加载中...</div>;
  if (!isAuthenticated) return <div>请登录</div>;
  
  return <div>已登录内容</div>;
}
```

### 2. 条件渲染

```typescript
import { useAuthContent } from '@/components/auth';

function MyComponent() {
  const { renderAuthContent } = useAuthContent();

  return renderAuthContent(
    <div>登录后显示</div>,
    <div>未登录显示</div>,
    <div>加载中...</div>
  );
}
```

### 3. 认证相关跳转

```typescript
import { authUtils } from '@/components/auth';

function MyComponent() {
  const handleLogin = () => authUtils.login();
  const handleRegister = () => authUtils.register();
  const handleLogout = () => authUtils.logout();
  const handleAccountCenter = () => authUtils.accountCenter();

  return (
    <div>
      <button onClick={handleLogin}>登录</button>
      <button onClick={handleRegister}>注册</button>
      <button onClick={handleLogout}>登出</button>
      <button onClick={handleAccountCenter}>账户中心</button>
    </div>
  );
}
```

## 测试

可以通过以下方式测试认证系统：
- 清除cookie后访问应用，应该自动重定向到外部登录中心
- 登录后访问应用，应该正常显示内容
- 检查cookie中是否有access_token

## 注意事项

1. **Token安全**：Token由外部登录中心管理，存储在cookie中
2. **错误处理**：所有认证错误都会自动重定向到外部登录中心
3. **性能优化**：认证状态持久化，页面刷新不会丢失登录状态
4. **用户体验**：登录后自动重定向到原始页面

## 故障排除

### 1. 无限重定向
- 检查环境变量配置
- 确认外部登录系统回调URL正确

### 2. Token失效
- 检查cookie中的access_token
- 确认外部登录中心正常工作

### 3. 用户信息获取失败
- 检查用户信息API接口
- 确认token有效性

### 4. 中间件不生效
- 检查 `middleware.ts` 配置
- 确认路由匹配规则正确
