# 批量压缩功能技术文档

## 1. 功能概述

批量压缩功能允许用户对多张图片进行压缩处理，支持两种压缩方式：
1. **按压缩程度**：提供原始、轻度、中度、深度四个预设压缩级别
2. **自定义体积**：用户可指定目标文件大小（KB/MB），系统自动调整压缩参数

## 2. 技术架构

### 2.1 核心模块结构

```
src/
├── lib/utils/imageCompress.ts          # 压缩核心算法
├── components/batch-editor/
│   ├── components/CompressSettings.tsx # 压缩设置UI组件
│   └── index.tsx                       # 批量编辑器主组件
├── lib/store/imageStore.ts             # 状态管理
└── components/batch-editor/components/
    └── ImageUploadArea.tsx             # 图片展示组件
```

### 2.2 数据流架构

```mermaid
graph TD
    A[用户选择压缩设置] --> B[CompressSettings组件]
    B --> C[handleApplyCompress函数]
    C --> D[imageStore.batchCompressImages]
    D --> E[imageCompress.compressImage]
    E --> F[Canvas API压缩处理]
    F --> G[更新图片状态]
    G --> H[UI显示压缩结果]
```

## 3. 核心实现

### 3.1 压缩算法实现 (`imageCompress.ts`)

#### 3.1.1 压缩程度映射
```typescript
function getQualityByLevel(level: CompressionLevel): number {
  switch (level) {
    case 'original': return 1.0;    // 原始质量
    case 'light':    return 0.9;    // 轻度压缩（最清晰）
    case 'medium':   return 0.7;    // 中度压缩（品质均衡）
    case 'deep':     return 0.5;    // 深度压缩（最小体积）
  }
}
```

#### 3.1.2 Canvas压缩实现
```typescript
async function compressImageWithQuality(
  imageUrl: string,
  quality: number,
  format: SupportedFormat = 'jpg'
): Promise<string> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';
    
    img.onload = () => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      canvas.width = img.width;
      canvas.height = img.height;
      ctx.drawImage(img, 0, 0);
      
      // 关键：使用toDataURL的quality参数进行压缩
      const compressedDataUrl = canvas.toDataURL(
        format === 'jpg' ? 'image/jpeg' : `image/${format}`, 
        quality
      );
      resolve(compressedDataUrl);
    };
    
    img.src = imageUrl;
  });
}
```

#### 3.1.3 自定义体积压缩算法
采用二分查找算法，自动寻找最佳压缩质量：

```typescript
async function compressToTargetSize(
  imageUrl: string,
  targetSizeBytes: number,
  format: SupportedFormat = 'jpg',
  maxIterations: number = 10
): Promise<{ dataUrl: string; quality: number }> {
  let minQuality = 0.1;
  let maxQuality = 1.0;
  
  for (let i = 0; i < maxIterations; i++) {
    const currentQuality = (minQuality + maxQuality) / 2;
    const compressedDataUrl = await compressImageWithQuality(
      imageUrl, currentQuality, format
    );
    const currentSize = getImageSizeFromDataUrl(compressedDataUrl);
    
    // 误差在5%以内则停止迭代
    if (Math.abs(currentSize - targetSizeBytes) / targetSizeBytes < 0.05) {
      break;
    }
    
    if (currentSize > targetSizeBytes) {
      maxQuality = currentQuality;
    } else {
      minQuality = currentQuality;
    }
  }
  
  return bestResult;
}
```

### 3.2 状态管理实现 (`imageStore.ts`)

#### 3.2.1 图片状态扩展
```typescript
export interface ImageState {
  // ... 原有字段
  
  // 压缩相关状态
  originalSize?: number;              // 原始文件大小（字节）
  compressedSize?: number;            // 压缩后文件大小（字节）
  compressedUrl?: string | null;      // 压缩后的图片URL
  compressionLevel?: 'original' | 'light' | 'medium' | 'deep';
  customCompressionSize?: number;     // 自定义压缩大小
  customCompressionUnit?: 'KB' | 'MB'; // 自定义压缩单位
}
```

#### 3.2.2 批量压缩实现
```typescript
batchCompressImages: async (
  imageIds: string[],
  level?: 'original' | 'light' | 'medium' | 'deep',
  customSize?: number,
  customUnit?: 'KB' | 'MB',
  onProgress?: (current: number, total: number, currentImageId?: string) => void
) => {
  const total = imageIds.length;
  let completed = 0;

  // 逐个处理图片，避免浏览器阻塞
  for (let i = 0; i < imageIds.length; i++) {
    const imageId = imageIds[i];
    
    try {
      await get().compressImage(imageId, level, customSize, customUnit);
    } catch (error) {
      console.error(`批量压缩中图片 ${imageId} 处理失败:`, error);
    }

    completed++;
    if (onProgress) {
      await onProgress(completed, total, imageId);
    }

    // 添加延迟避免浏览器阻塞
    if (i < imageIds.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 50));
    }
  }
}
```

### 3.3 UI组件实现 (`CompressSettings.tsx`)

#### 3.3.1 压缩模式切换
```typescript
const [compressionMode, setCompressionMode] = useState<'level' | 'custom'>('level');

// 模式切换UI
<div className='bg-[#F5F5F5] rounded-lg p-1'>
  <button
    onClick={() => setCompressionMode('level')}
    className={cn(
      'flex-1 py-2 px-3 text-sm font-medium rounded-md transition-colors w-full',
      compressionMode === 'level'
        ? 'bg-white text-[#121212] shadow-sm'
        : 'text-[#878787] hover:text-[#121212]'
    )}
  >
    按压缩程度
  </button>
  <button
    onClick={() => setCompressionMode('custom')}
    className={cn(
      'flex-1 py-2 px-3 text-sm font-medium rounded-md transition-colors w-full mt-1',
      compressionMode === 'custom'
        ? 'bg-white text-[#121212] shadow-sm'
        : 'text-[#878787] hover:text-[#121212]'
    )}
  >
    自定义体积
  </button>
</div>
```

#### 3.3.2 自定义体积输入验证
```typescript
const handleApply = () => {
  if (compressionMode === 'custom') {
    const sizeNum = parseFloat(customSize);
    
    if (isNaN(sizeNum) || sizeNum <= 0) {
      showTips('error', 'Please enter a valid file size.');
      return;
    }

    // 验证大小范围
    const sizeInKB = customUnit === 'MB' ? sizeNum * 1024 : sizeNum;
    if (sizeInKB < 1) {
      showTips('error', 'File size must be at least 1 KB.');
      return;
    }
    if (sizeInKB > 10240) { // 10MB
      showTips('error', 'File size cannot exceed 10 MB.');
      return;
    }

    onApply({
      customSize: sizeNum,
      customUnit,
    });
  }
};
```

## 4. 文件大小显示实现

### 4.1 大小格式化函数
```typescript
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}
```

### 4.2 UI显示实现
```typescript
{/* 显示文件大小变化 */}
{image.compressedSize && image.originalSize ? (
  <div className='flex items-center justify-start gap-1'>
    <span>{formatFileSize(image.originalSize)}</span>
    <span>→</span>
    <span
      className='px-2 py-1 rounded text-sm font-medium'
      style={{ backgroundColor: '#D7EFCE', color: '#2D5A3D' }}
    >
      {formatFileSize(image.compressedSize)}
    </span>
  </div>
) : (
  image.size > 0 && <span>{formatFileSize(image.size)}</span>
)}
```

## 5. 性能优化策略

### 5.1 批量处理优化
- **串行处理**：避免并发压缩导致浏览器卡顿
- **延迟控制**：每张图片处理间隔50ms，保证UI响应性
- **进度反馈**：实时显示处理进度，提升用户体验

### 5.2 内存管理
- **URL清理**：及时释放blob URL，避免内存泄漏
- **Canvas复用**：每次压缩后清理canvas上下文
- **分块处理**：大文件分块处理，避免内存溢出

### 5.3 错误处理
```typescript
try {
  await get().compressImage(imageId, level, customSize, customUnit);
} catch (error) {
  console.error(`批量压缩中图片 ${imageId} 处理失败:`, error);
  // 继续处理下一张图片，不中断整个流程
}
```

## 6. 技术特点

### 6.1 算法优势
- **二分查找**：自定义体积压缩采用二分查找，快速收敛到目标大小
- **质量控制**：支持精确的质量参数控制，满足不同压缩需求
- **格式兼容**：支持多种图片格式的压缩处理

### 6.2 用户体验
- **实时预览**：压缩前后大小对比显示
- **进度提示**：批量处理进度实时反馈
- **错误处理**：友好的错误提示和处理机制

### 6.3 扩展性
- **模块化设计**：压缩算法独立封装，易于扩展
- **配置灵活**：支持多种压缩参数配置
- **状态管理**：完整的状态追踪和历史记录

## 7. 使用示例

### 7.1 按压缩程度压缩
```typescript
// 轻度压缩
await compressImage(imageUrl, { level: 'light' });

// 深度压缩
await compressImage(imageUrl, { level: 'deep' });
```

### 7.2 自定义体积压缩
```typescript
// 压缩到500KB
await compressImage(imageUrl, { 
  customSize: 500, 
  customUnit: 'KB' 
});

// 压缩到2MB
await compressImage(imageUrl, { 
  customSize: 2, 
  customUnit: 'MB' 
});
```

## 8. 后续优化方向

1. **WebWorker支持**：将压缩处理移至Worker线程，避免主线程阻塞
2. **WASM优化**：使用WebAssembly实现更高效的图片压缩算法
3. **智能压缩**：基于图片内容特征自动选择最佳压缩参数
4. **批量预览**：支持压缩前批量预览效果
5. **云端压缩**：支持服务端压缩，处理大文件和复杂压缩需求
