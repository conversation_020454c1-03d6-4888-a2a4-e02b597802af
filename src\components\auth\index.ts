// 认证相关组件统一导出

export { AuthGuard, useAuthCheck, useAuthContent } from './AuthGuard';
export { AuthProvider } from './AuthProvider';

//  说明：
// - AuthProvider: 专门负责认证系统初始化，不包含UI逻辑
// - AuthGuard: 专门负责认证保护，检查状态并控制访问权限
// - 使用时应该: <AuthProvider><AuthGuard>{children}</AuthGuard></AuthProvider>

// 认证状态管理
export {
  useAuthStore,
  type UserInfo,
  type AuthState,
  type AuthActions,
  MemberLevel,
} from '@/store/accountStore';

// 缓存管理
export { getAccessToken } from '@/lib/cache';

// 认证工具
export { authUtils } from '@/lib/authUtils';
