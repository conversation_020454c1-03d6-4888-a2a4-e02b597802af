# 认证系统架构文档

## 1. 系统概述

### 1.1 设计原则
- **强制认证**: 无游客模式，所有功能需要登录
- **外部集成**: 与第三方登录中心深度集成
- **安全优先**: Token由外部系统管理，本地只读取
- **用户体验**: 无感知的认证流程和自动重定向

### 1.2 核心特性
- 中间件级别的路由保护
- 自动Token检测和验证
- 外部登录中心集成
- 用户状态持久化
- 统一的认证工具函数

## 2. 架构设计

### 2.1 认证流程图

```mermaid
graph TD
    A[用户访问应用] --> B{中间件检查Cookie}
    B -->|有Token| C[验证Token有效性]
    B -->|无Token| D[重定向到外部登录中心]
    C -->|有效| E[正常访问应用]
    C -->|无效| D
    D --> F[用户在外部系统登录]
    F --> G[外部系统种植Cookie]
    G --> H[回调到原页面]
    H --> E
    E --> I{Token失效?}
    I -->|是| D
    I -->|否| J[继续使用应用]
```

### 2.2 组件架构

```
认证系统组件层次:

├── middleware.ts                    # Next.js 中间件 (最外层保护)
│   ├── 检查所有路由的Cookie
│   ├── 无Token时重定向到外部登录中心
│   └── 有Token时允许继续访问
│
├── AuthProvider                     # 认证提供者 (应用级)
│   ├── 初始化认证状态
│   ├── 监听页面可见性变化
│   └── 包装整个应用
│
├── AuthGuard                        # 认证守卫 (组件级)
│   ├── 检查认证状态
│   ├── 显示加载状态
│   └── 未认证时重定向
│
├── authUtils                        # 认证工具函数
│   ├── login() - 跳转登录
│   ├── register() - 跳转注册
│   ├── logout() - 登出并清理
│   └── accountCenter() - 账户中心
│
└── useAuthStore                     # 认证状态管理
    ├── 用户信息存储
    ├── 认证状态管理
    └── Token验证逻辑
```

## 3. 核心组件详解

### 3.1 中间件 (middleware.ts)

**职责**: 路由级别的认证保护
**位置**: 项目根目录
**工作原理**:
```typescript
export function middleware(request: NextRequest) {
  const token = request.cookies.get('access_token')?.value;
  
  if (!token) {
    // 重定向到外部登录中心，带上redirect参数
    const loginUrl = process.env.NEXT_PUBLIC_LOGIN_URL;
    const currentPath = pathname + request.nextUrl.search;
    const redirectUrl = `${loginUrl}?redirect=${currentPath}`;
    return NextResponse.redirect(redirectUrl);
  }
  
  return NextResponse.next();
}
```

### 3.2 认证提供者 (AuthProvider) - 重构后

**职责**: 专门负责认证系统初始化，不包含UI逻辑
**位置**: `src/components/auth/AuthProvider.tsx`
**核心功能**:
- 应用启动时初始化认证状态
- 检查token有效性并更新用户信息
- 为整个应用提供认证上下文
- **不再包装AuthGuard**，职责单一

**重构改进**:
- 移除了重复的initialize调用
- 不再直接包装AuthGuard组件
- 专注于认证状态的初始化工作

### 3.3 认证守卫 (AuthGuard) - 重构后

**职责**: 专门负责认证保护，不处理初始化逻辑
**位置**: `src/components/auth/AuthGuard.tsx`
**工作流程**:
1. 检查认证状态（不再调用initialize）
2. 显示加载状态
3. 未认证时重定向到外部登录中心
4. 已认证时渲染子组件

**重构改进**:
- 移除了重复的initialize调用
- 依赖AuthProvider完成的初始化工作
- 专注于认证状态检查和访问控制

**使用方式**:
```typescript
<AuthProvider>
  <AuthGuard>{children}</AuthGuard>
</AuthProvider>
```

### 3.4 认证状态管理 (useAuthStore)

**职责**: 全局认证状态管理
**位置**: `src/store/accountStore.ts`
**核心状态**:
```typescript
interface AuthState {
  isAuthenticated: boolean;
  isLoading: boolean;
  userInfo: UserInfo;
}
```

**核心方法**:
- `initialize()`: 初始化认证状态
- `checkAuth()`: 检查Token有效性
- `updateUserInfo()`: 更新用户信息

### 3.5 认证工具函数 (authUtils)

**职责**: 统一的认证相关操作
**位置**: `src/lib/authUtils.ts`
**核心功能**:
```typescript
export const authUtils = {
  login: (redirectUrl?) => void,      // 跳转登录
  register: (redirectUrl?) => void,   // 跳转注册
  logout: (redirectUrl?) => void,     // 登出并清理
  accountCenter: () => void,          // 账户中心
};
```

## 4. 数据流

### 4.1 认证检查流程

```
1. 用户访问 → middleware检查Cookie → 有Token继续，无Token重定向
2. 应用启动 → AuthProvider初始化 → 检查Token有效性
3. 组件渲染 → AuthGuard检查 → 认证状态决定渲染内容
4. API请求 → HTTP客户端自动添加Token → Token失效自动重定向
```

### 4.2 登出流程

```
1. 用户点击登出 → authUtils.logout()
2. 清理本地Token → removeAccessToken()
3. 重定向到外部登录中心 → 带上redirect参数
4. 外部系统清理Session → 完成登出
```

## 5. 环境配置

### 5.1 必需的环境变量

```bash
# 外部登录系统URL
NEXT_PUBLIC_LOGIN_URL="https://account-test.tenorshare.ai/#/login"

# 注册页面URL
NEXT_PUBLIC_REGISTER_URL="https://account-test.tenorshare.ai/#/signUp"

# 账户管理URL
NEXT_PUBLIC_ACCOUNT_URL="https://account-test.tenorshare.ai"

# API基础URL
NEXT_PUBLIC_API_BASE_URL="https://api-test.tenorshare.ai/pixpretty"
```

### 5.2 Cookie配置

- **名称**: `access_token`
- **管理方**: 外部登录中心
- **读取方**: 本应用 (只读)
- **作用域**: 跨域共享

## 6. 安全考虑

### 6.1 Token安全
- Token由外部系统管理，本地不存储敏感信息
- 使用HttpOnly Cookie，防止XSS攻击
- 自动Token失效检测和重定向

### 6.2 路由安全
- 中间件级别的全局保护
- 无法绕过的认证检查
- 自动重定向机制

### 6.3 状态安全
- 认证状态持久化，防止页面刷新丢失
- 页面可见性变化时重新验证
- 错误状态的优雅处理

## 7. 性能优化

### 7.1 加载优化
- 认证组件懒加载
- 状态持久化减少重复检查
- 智能的重新验证机制

### 7.2 用户体验优化
- 无感知的认证流程
- 优雅的加载状态
- 自动保存用户访问路径

## 8. 故障处理

### 8.1 常见问题
- **无限重定向**: 检查环境变量配置
- **Token失效**: 检查外部登录中心状态
- **用户信息获取失败**: 检查API接口

### 8.2 错误恢复
- 自动重试机制
- 降级处理方案
- 用户友好的错误提示

## 9. 扩展性

### 9.1 支持多种登录方式
- 当前支持外部登录中心
- 可扩展支持OAuth、SAML等
- 统一的认证接口设计

### 9.2 多环境支持
- 开发、测试、生产环境配置
- 环境变量驱动的配置
- 灵活的部署方案
