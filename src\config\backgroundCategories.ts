/**
 * 背景图片分类配置
 * 统一管理所有背景图片分类的配置信息
 */

/**
 * 背景分类配置项
 */
export interface BackgroundCategoryConfig {
  /** 分类名称（英文） */
  nameEn: string;
  /** 分类名称（中文） */
  nameCn: string;
  /** 该分类下的图片数量 */
  imageCount: number;
}

/**
 * 背景图片分类配置
 * 包含所有预设背景图片分类的配置信息
 */
export const BACKGROUND_CATEGORIES_CONFIG = {
  gradient: { nameEn: 'Gradient', nameCn: '渐变', imageCount: 6 },
  landscape: { nameEn: 'Landscape', nameCn: '风景', imageCount: 6 },
  geometry: { nameEn: 'Geometric', nameCn: '几何', imageCount: 6 },
  grain: { nameEn: 'Wood', nameCn: '木纹', imageCount: 6 },
  paper: { nameEn: 'Paper', nameCn: '纸张', imageCount: 6 },
  texture: { nameEn: 'Texture', nameCn: '纹理', imageCount: 6 },
} as const;

/**
 * 背景分类ID类型
 */
export type BackgroundCategoryId = keyof typeof BACKGROUND_CATEGORIES_CONFIG;

/**
 * 预设的背景颜色列表
 * 包含透明色和常用的颜色选项
 */
export const PREDEFINED_COLORS: string[] = [
  'transparent',
  '#000000',
  '#FFFFFF',
  '#E7E7E7',
  '#878787',
  '#FF3B30',
  '#FF2D55',
  '#AF52DE',
  '#5856D6',
  '#007AFF',
  '#32ADE6',
  '#64D2FF',
  '#00C7BE',
  '#00B06B',
  '#96D035',
  '#C2E62C',
  '#FFD60A',
  '#FFC300',
  '#FF9500',
  '#FF6B00',
  '#FF3B2F',
];

/**
 * 预设颜色类型
 */
export type PredefinedColor = (typeof PREDEFINED_COLORS)[number];
