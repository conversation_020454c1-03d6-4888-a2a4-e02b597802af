'use client';

import { useAuthStore } from '@/store/accountStore';
import { authUtils } from '@/lib/authUtils';

interface AuthGuardProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

/**
 * 认证守卫组件
 * 专门负责认证保护，不处理初始化逻辑
 * 职责：
 * - 检查用户认证状态
 * - 显示加载状态
 * - 未认证时重定向到外部登录中心
 * - 已认证时渲染受保护的内容
 *
 * 注意：认证初始化由AuthProvider负责，此组件只负责状态检查和保护
 */
export function AuthGuard({
  children,
  fallback = <AuthLoadingFallback />,
}: AuthGuardProps) {
  const { isAuthenticated, isLoading } = useAuthStore();

  // 正在加载中，显示加载状态
  if (isLoading) {
    return <>{fallback}</>;
  }

  // 未认证，重定向到外部登录中心
  if (!isAuthenticated) {
    const loginUrl = process.env.NEXT_PUBLIC_LOGIN_URL;

    if (loginUrl && typeof window !== 'undefined') {
      // 使用统一的URL构建函数
      const redirectUrl = authUtils.buildRedirectUrl(loginUrl);

      // 重定向到外部登录中心
      window.location.href = redirectUrl;

      // 在重定向期间显示加载状态
      return <>{fallback}</>;
    }

    // 如果没有配置登录URL，显示fallback
    return <>{fallback}</>;
  }

  // 已认证，渲染子组件
  return <>{children}</>;
}

/**
 * 默认的加载状态组件
 */
function AuthLoadingFallback() {
  return (
    <div className='min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100'>
      <div className='text-center'>
        <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4'></div>
        <p className='text-gray-600 text-lg'>正在验证身份...</p>
      </div>
    </div>
  );
}

/**
 * 轻量级认证检查Hook
 * 用于在组件中快速检查认证状态
 */
export function useAuthCheck() {
  const { isAuthenticated, isLoading } = useAuthStore();

  return {
    isAuthenticated,
    isLoading,
    isReady: !isLoading,
  };
}

/**
 * 条件渲染认证内容的Hook
 * 根据认证状态渲染不同内容
 */
export function useAuthContent() {
  const { isAuthenticated, isLoading } = useAuthStore();

  const renderAuthContent = (
    authenticatedContent: React.ReactNode,
    unauthenticatedContent?: React.ReactNode,
    loadingContent?: React.ReactNode
  ) => {
    if (isLoading) {
      return loadingContent || <AuthLoadingFallback />;
    }

    if (isAuthenticated) {
      return authenticatedContent;
    }

    return unauthenticatedContent || null;
  };

  return { renderAuthContent, isAuthenticated, isLoading };
}
