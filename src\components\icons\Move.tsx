import React from 'react';

const MoveIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width='24'
    height='24'
    viewBox='0 0 24 24'
    fill='none'
    className={className}
  >
    <path
      d='M12.5085 22C9.35703 22 7.40469 20.7558 6.0965 18.2582C5.34334 16.8737 4.75863 15.2086 4.13426 13.2816C3.87658 12.4958 3.5 11.4948 3.5 11.0458C3.5 10.5407 3.89646 10.2039 4.45139 10.2039C5.08565 10.2039 5.45234 10.5781 5.91814 11.4668L7.28578 14.1983C7.43437 14.4976 7.59294 14.6099 7.7515 14.6099C7.94973 14.6099 8.13806 14.4976 8.08851 14.1703L6.88933 4.41347C6.81998 3.79607 7.24612 3.4406 7.73171 3.34705C8.25697 3.25351 8.85157 3.49673 8.96057 4.10477L10.1283 10.8484C10.1759 11.1232 10.4458 11.2934 10.717 11.2283V11.2283C10.9299 11.1773 11.1013 10.9891 11.1013 10.7702V2.97287C11.1013 2.43966 11.5769 2 12.1616 2C12.7464 2 13.2221 2.43966 13.2221 2.97287V10.5879C13.2221 10.8451 13.4251 11.0576 13.6802 11.0894V11.0894C13.9483 11.1229 14.1945 10.9399 14.2326 10.6724L15.1843 3.97381C15.2735 3.31899 15.8384 3.07577 16.3735 3.15996C16.879 3.2348 17.3547 3.63704 17.2754 4.25444L16.3299 11.4479C16.3028 11.6537 16.4254 11.8574 16.61 11.9525V11.9525C16.8986 12.1011 17.2481 11.9408 17.3171 11.6235L18.4349 6.48082C18.5736 5.84472 19.1286 5.61085 19.634 5.68569C20.1296 5.76988 20.5854 6.17212 20.4863 6.81759L19.2971 15.4331C18.7322 19.5398 16.4429 22 12.5085 22Z'
      strokeWidth='1.2'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
  </svg>
);

export default MoveIcon;
