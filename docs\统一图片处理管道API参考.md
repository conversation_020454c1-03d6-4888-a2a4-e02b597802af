# 统一图片处理管道 API 参考

## 核心函数

### processImagePipeline

统一的图片处理管道，将多种操作按顺序应用到同一个canvas上。

```typescript
function processImagePipeline(
  imageUrl: string,
  processedImageUrl: string | null,
  config: ImageProcessingConfig
): Promise<ProcessingResult>
```

**参数：**
- `imageUrl`: 原始图片URL
- `processedImageUrl`: 去背后的图片URL（可选）
- `config`: 处理配置对象

**返回值：**
```typescript
interface ProcessingResult {
  dataUrl: string;      // 处理后的图片数据URL
  width: number;        // 最终宽度
  height: number;       // 最终高度
  size: number;         // 文件大小（字节）
  format: SupportedFormat; // 输出格式
}
```

**示例：**
```typescript
const result = await processImagePipeline(
  'blob:http://localhost:3000/original',
  'blob:http://localhost:3000/processed',
  {
    backgroundColor: '#ffffff',
    targetWidth: 400,
    targetHeight: 300,
    resizeMode: 'fit',
    outputFormat: 'jpg',
    compressionLevel: 'medium'
  }
);
```

### generatePreviewUrl

生成预览URL的轻量版处理管道，用于实时预览。

```typescript
function generatePreviewUrl(
  imageUrl: string,
  processedImageUrl: string | null,
  config: Pick<ImageProcessingConfig, 'backgroundColor' | 'backgroundImageUrl' | 'targetWidth' | 'targetHeight' | 'resizeMode'>
): Promise<string>
```

**参数：**
- `imageUrl`: 原始图片URL
- `processedImageUrl`: 去背后的图片URL（可选）
- `config`: 预览配置对象（仅包含背景和尺寸相关配置）

**返回值：**
- `Promise<string>`: 预览图片的数据URL

**示例：**
```typescript
const previewUrl = await generatePreviewUrl(
  image.previewUrl,
  image.processedUrl,
  {
    backgroundColor: '#ff0000',
    targetWidth: 400,
    targetHeight: 300,
    resizeMode: 'fit'
  }
);
```

### getImageDownloadInfo

获取图片的下载信息，使用统一处理管道生成最终图片。

```typescript
function getImageDownloadInfo(image: ImageState): Promise<DownloadInfo>
```

**参数：**
- `image`: 图片状态对象

**返回值：**
```typescript
interface DownloadInfo {
  url: string;          // 下载URL
  fileName: string;     // 文件名
  dimensions: string;   // 尺寸信息（如 "400x300"）
  format: string;       // 文件格式
}
```

**示例：**
```typescript
const downloadInfo = await getImageDownloadInfo(imageState);
// 创建下载链接
const link = document.createElement('a');
link.href = downloadInfo.url;
link.download = downloadInfo.fileName;
link.click();
```

## 配置接口

### ImageProcessingConfig

完整的图片处理配置接口。

```typescript
interface ImageProcessingConfig {
  // 背景处理
  backgroundColor?: string;           // 背景颜色（如 '#ffffff'）
  backgroundImageUrl?: string;        // 背景图片URL
  
  // 尺寸调整
  targetWidth?: number;               // 目标宽度（像素）
  targetHeight?: number;              // 目标高度（像素）
  resizeMode?: ResizeMode;            // 调整模式
  
  // 格式转换
  outputFormat?: SupportedFormat;     // 输出格式
  
  // 压缩设置
  compressionLevel?: CompressionLevel; // 压缩级别
  customCompressionSize?: number;      // 自定义压缩大小
  customCompressionUnit?: 'KB' | 'MB'; // 压缩单位
  
  // 其他设置
  quality?: number;                   // 输出质量 (0-1)
}
```

### ResizeMode

图片尺寸调整模式。

```typescript
type ResizeMode = 'fit' | 'fill' | 'stretch';
```

- `fit`: 适应模式，保持宽高比，图片完全显示在目标尺寸内
- `fill`: 填充模式，保持宽高比，裁剪图片以填满目标尺寸
- `stretch`: 拉伸模式，不保持宽高比，强制拉伸到目标尺寸

### CompressionLevel

压缩级别枚举。

```typescript
type CompressionLevel = 'original' | 'light' | 'medium' | 'deep';
```

- `original`: 原始质量（quality: 1.0）
- `light`: 轻度压缩（quality: 0.8）
- `medium`: 中度压缩（quality: 0.7）
- `deep`: 深度压缩（quality: 0.5）

### SupportedFormat

支持的图片格式。

```typescript
type SupportedFormat = 'png' | 'jpg' | 'jpeg' | 'webp';
```

## Store 方法

### updateImagePreviewUrl

更新单个图片的预览URL。

```typescript
updateImagePreviewUrl(imageId: string): Promise<void>
```

**参数：**
- `imageId`: 图片ID

**示例：**
```typescript
await useImageStore.getState().updateImagePreviewUrl('img-001');
```

### batchUpdatePreviewUrls

批量更新多个图片的预览URL。

```typescript
batchUpdatePreviewUrls(imageIds: string[]): Promise<void>
```

**参数：**
- `imageIds`: 图片ID数组

**示例：**
```typescript
const imageIds = ['img-001', 'img-002', 'img-003'];
await useImageStore.getState().batchUpdatePreviewUrls(imageIds);
```

## 工具函数

### loadImage

加载图片对象的工具函数。

```typescript
function loadImage(src: string): Promise<HTMLImageElement>
```

### estimateFileSize

估算文件大小的工具函数。

```typescript
function estimateFileSize(dataUrl: string): number
```

### calculateCompressionQuality

计算压缩质量的工具函数。

```typescript
function calculateCompressionQuality(config: ImageProcessingConfig): number
```

## 错误处理

所有异步函数都会抛出错误，需要适当的错误处理：

```typescript
try {
  const result = await processImagePipeline(imageUrl, processedUrl, config);
  // 处理成功
} catch (error) {
  console.error('处理失败:', error);
  // 错误处理逻辑
}
```

## 性能建议

1. **预览优化**：使用 `generatePreviewUrl` 而不是完整的 `processImagePipeline`
2. **批量处理**：使用 `Promise.all` 并发处理多个图片
3. **内存管理**：及时清理生成的 blob URL
4. **错误回退**：提供回退机制，确保用户体验

## 使用流程

### 典型的批量处理流程

```typescript
// 1. 用户上传图片后，图片存储在 ImageStore 中

// 2. 用户进行批量操作（如设置背景）
const handleApplyBackground = async (backgroundColor: string) => {
  // 更新所有图片的背景设置
  useImageStore.setState(state => {
    images.forEach(image => {
      const existingImage = state.images.get(image.id);
      if (existingImage) {
        existingImage.backgroundColor = backgroundColor;
      }
    });
  });
  
  // 批量更新预览URL
  const imageIds = images.map(img => img.id);
  await useImageStore.getState().batchUpdatePreviewUrls(imageIds);
};

// 3. 用户下载图片
const handleDownload = async (imageId: string) => {
  const image = useImageStore.getState().images.get(imageId);
  if (image) {
    const downloadInfo = await getImageDownloadInfo(image);
    // 创建下载链接
    const link = document.createElement('a');
    link.href = downloadInfo.url;
    link.download = downloadInfo.fileName;
    link.click();
  }
};
```

这个API参考提供了统一图片处理管道的所有核心接口和使用方法，便于开发者快速集成和使用。
