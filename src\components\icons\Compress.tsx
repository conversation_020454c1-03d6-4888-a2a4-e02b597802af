import React from 'react';

interface IconProps {
  className?: string;
  size?: number;
}

const CompressIcon: React.FC<IconProps> = ({ className, size = 24 }) => (
  <svg
    width={size}
    height={size}
    viewBox='0 0 24 24'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    className={className}
  >
    <path
      d='M3 3L9 9'
      strokeWidth='1.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
    <path
      d='M3 21L9 15'
      strokeWidth='1.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
    <path
      d='M21 3L15 9'
      strokeWidth='1.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
    <path
      d='M21 21L15 15'
      strokeWidth='1.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
    <path
      d='M10 3V8C10 9.10457 9.10457 10 8 10H3'
      strokeWidth='1.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
    <path
      d='M10 21V16C10 14.8954 9.10457 14 8 14H3'
      strokeWidth='1.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
    <path
      d='M14 3V8C14 9.10457 14.8954 10 16 10H21'
      strokeWidth='1.71429'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
    <path
      d='M14 21V16C14 14.8954 14.8954 14 16 14H21'
      strokeWidth='1.71429'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
  </svg>
);

export default CompressIcon;
