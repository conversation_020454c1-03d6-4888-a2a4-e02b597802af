import { httpClient } from './http-client';

// 认证相关的接口类型定义
export interface UserInfo {
  id: string;
  username: string;
  email?: string;
  avatar?: string;
  role?: string;
  permissions?: string[];
}

// 认证相关 API 函数

/**
 * 获取当前用户信息
 */
export async function getUserInfo(): Promise<UserInfo> {
  return httpClient.get<UserInfo>('/api/v1/user/info');
}

/**
 * 获取游客信息
 */
export async function getGuestInfo(): Promise<UserInfo> {
  return httpClient.get<UserInfo>('/api/v1/guest/info');
}

export interface HistoryData {
  type: string;
  way: string;
  remark: string;
  create_time: string;
  auth_type: string;
  num: number;
  time: number;
  date: string;
}

interface GetHistoryDataResponse {
  list: HistoryData[];
  pager: {
    page: number;
    page_size: number;
    total: number;
  };
}

interface GetHistoryDataRequest {
  page: number;
  page_size: number;
  start_time: number;
  end_time: number;
}

/**
 * 获取历史数据
 */
export async function getHistoryData({
  page,
  page_size,
  start_time,
  end_time,
}: GetHistoryDataRequest): Promise<GetHistoryDataResponse> {
  return httpClient.get<GetHistoryDataResponse>('/api/v1/permission/history', {
    page,
    page_size,
    start_time,
    end_time,
  });
}
