import { NextRequest, NextResponse } from 'next/server';

/**
 * 中间件处理函数
 * 检查cookie中的token，未登录时重定向到外部登录中心
 */
export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // 获取token
  const token = request.cookies.get('access_token')?.value;

  // 对于所有路由，检查是否有token
  if (!token) {
    // 构建外部登录URL，包含重定向参数
    const loginUrl = process.env.NEXT_PUBLIC_LOGIN_URL;
    if (loginUrl) {
      // 构建完整的回调URL
      const protocol = request.nextUrl.protocol;
      const host = request.nextUrl.host;
      const currentPath = pathname + request.nextUrl.search;
      const fullCallbackUrl = `${protocol}//${host}${currentPath}`;

      const redirectUrl = `${loginUrl}?redirect=${fullCallbackUrl}`;
      return NextResponse.redirect(redirectUrl);
    }
  }

  // 有token，继续处理请求
  return NextResponse.next();
}

export const config = {
  matcher: [
    // 匹配需要处理的路径（排除静态资源和API路由）
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$|api/).*)',
  ],
};
