import React from 'react';

interface IconProps {
  className?: string;
  size?: number;
}

const BackgroundRemoveIcon: React.FC<IconProps> = ({
  className,
  size = 24,
}) => (
  <svg
    width={size}
    height={size}
    viewBox='0 0 24 24'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    className={className}
  >
    <path
      d='M22 7.64328C22 6.06532 20.7208 4.78613 19.1429 4.78613H12V19.7861H19.1429C20.7208 19.7861 22 18.5069 22 16.929V7.64328Z'
      strokeWidth='1.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
    <path
      d='M17 5L12 11'
      strokeWidth='1.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
    <path
      d='M22 13L16.5 19.5'
      strokeWidth='1.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
    <path
      d='M21.5 6.5L12 18.5'
      strokeWidth='1.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
    <path
      d='M12 3L12 21.5714'
      strokeWidth='1.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
    <path
      d='M9 4.78613H4.85714C3.27919 4.78613 2 6.06532 2 7.64328V16.929C2 18.5069 3.27919 19.7861 4.85714 19.7861H9'
      strokeWidth='1.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
  </svg>
);

export default BackgroundRemoveIcon;
