'use client';

import React, { useState } from 'react';
import { cn } from '@/lib/base';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/Select';
import { Lock, Unlock } from 'lucide-react';
import Image from 'next/image';
import { Slider } from '@/components/ui/Slider';
import { useTips } from '@/components/ui/Tips';
import { shouldDisableBatchApplyButton } from '../../../lib/buttonUtils';
import {
  RESIZE_PRESETS,
  FixedPreset,
  RatioPreset,
} from '@/config/resizePresets';

interface ResizeSettings {
  width?: number;
  height?: number;
  aspectRatio?: number; // 宽高比 (width/height)
  scale?: number; // 缩放比例 (0.2-2.0)
  mode: 'fixed' | 'ratio' | 'scale' | 'custom' | 'original'; // 固定尺寸、宽高比、缩放、自定义模式或原尺寸
  customMode?: 'scale' | 'dimensions'; // 自定义模式的子类型
  unit?: 'px' | 'in' | 'mm'; // 单位
  lockAspectRatio?: boolean; // 是否锁定宽高比
}

interface ResizeSettingsProps {
  onApply?: (settings: ResizeSettings) => void;
  images?: Array<{
    id: string;
    status: string;
    processedUrl?: string | null;
    targetWidth?: number;
    targetHeight?: number;
    width?: number;
    height?: number;
  }>;
  isProcessing?: boolean;
}

export function ResizeSettings({
  onApply,
  images = [],
  isProcessing = false,
}: ResizeSettingsProps) {
  const { showTips } = useTips();
  const [selectedCategory, setSelectedCategory] = useState<
    'marketplace' | 'social' | 'ratio' | 'custom' | 'original'
  >('marketplace');
  const [selectedPreset, setSelectedPreset] = useState<string>('tiktok');

  // Custom Size 相关状态
  const [customMode, setCustomMode] = useState<'scale' | 'dimensions'>('scale');
  const [scaleValue, setScaleValue] = useState<number>(100); // 百分比 (20-200)
  const [customWidth, setCustomWidth] = useState<string>('');
  const [customHeight, setCustomHeight] = useState<string>('');
  const [lockAspectRatio, setLockAspectRatio] = useState<boolean>(true);
  const [unit, setUnit] = useState<'px' | 'in' | 'mm'>('px');

  // 单位转换函数
  const convertToPixels = (
    value: number,
    fromUnit: 'px' | 'in' | 'mm'
  ): number => {
    switch (fromUnit) {
      case 'px':
        return value;
      case 'in':
        return value * 96; // 1 inch = 96 pixels
      case 'mm':
        return value * 3.78; // 1 mm ≈ 3.78 pixels
      default:
        return value;
    }
  };

  // 验证尺寸函数
  const validateDimensions = (width: number, height: number): string => {
    const minPx = 300;
    const maxPx = 4000;

    if (width < minPx || height < minPx) {
      return `Size too small. Minimum value is 300 px (≈ 3.1 inches or 79 mm).`;
    }

    if (width > maxPx || height > maxPx) {
      return `Size too large. Maximum value is 4000 px (≈ 42 inches or 1066 mm).`;
    }

    return '';
  };

  const handleApply = () => {
    // Original Size 分类的特殊处理
    if (selectedCategory === 'original' && onApply) {
      // 还原到原始尺寸
      onApply({
        mode: 'original',
      });
      return;
    }

    // Custom Size 分类的特殊处理
    if (selectedCategory === 'custom' && onApply) {
      if (customMode === 'scale') {
        // 按比例缩放
        onApply({
          scale: scaleValue / 100, // 转换为小数
          mode: 'scale',
          customMode: 'scale',
        });
      } else if (customMode === 'dimensions') {
        // 自定义尺寸
        const widthNum = parseFloat(customWidth);
        const heightNum = parseFloat(customHeight);

        if (
          isNaN(widthNum) ||
          isNaN(heightNum) ||
          widthNum <= 0 ||
          heightNum <= 0
        ) {
          showTips('error', 'Please enter valid width and height values.');
          return;
        }

        // 转换为像素
        const widthPx = convertToPixels(widthNum, unit);
        const heightPx = convertToPixels(heightNum, unit);

        // 验证尺寸
        const error = validateDimensions(widthPx, heightPx);
        if (error) {
          showTips('error', error);
          return;
        }

        onApply({
          width: Math.round(widthPx),
          height: Math.round(heightPx),
          mode: 'custom',
          customMode: 'dimensions',
          unit,
          lockAspectRatio,
        });
      }
      return;
    }

    // 其他分类的处理
    const category = RESIZE_PRESETS[selectedCategory];
    const preset = category.presets.find(p => p.id === selectedPreset);

    if (preset && onApply) {
      if (preset.mode === 'fixed') {
        // 固定尺寸模式
        const fixedPreset = preset as FixedPreset;
        onApply({
          width: fixedPreset.width,
          height: fixedPreset.height,
          mode: 'fixed',
        });
      } else if (preset.mode === 'ratio') {
        // 宽高比模式
        const ratioPreset = preset as RatioPreset;
        onApply({
          aspectRatio: ratioPreset.aspectRatio,
          mode: 'ratio',
        });
      }
    }
  };

  return (
    <div className='w-[344px] bg-white border-r border-[#E7E7E7] flex flex-col h-full'>
      {/* 头部标题区域 */}
      <div className='pt-6 px-4'>
        <div className='border-b border-border pb-4'>
          <h2 className='text-base font-bold'>Resize</h2>
        </div>
      </div>

      {/* 分类选择下拉框 */}
      <div className='px-4 pt-4'>
        <Select
          value={selectedCategory}
          onValueChange={(
            value: 'marketplace' | 'social' | 'ratio' | 'custom' | 'original'
          ) => {
            setSelectedCategory(value);
            // Custom Size 和 Original Size 分类不需要预设，直接显示控制选项
            if (value === 'custom' || value === 'original') {
              setSelectedPreset(''); // 清空预设选择
            } else {
              // 其他分类切换时选择该分类下的第一个预设
              const firstPreset = RESIZE_PRESETS[value].presets[0];
              if (firstPreset) {
                setSelectedPreset(firstPreset.id);
              }
            }
          }}
        >
          <SelectTrigger
            size='lg'
            className='w-[312px] h-12 bg-white border border-[#E7E7E7] hover:border-[#E7E7E7] data-[state=open]:border-[#FFCC03] rounded-lg px-3 flex items-center justify-between gap-0.5 text-base font-normal text-text-primary transition-colors'
          >
            <SelectValue placeholder='Select a category' />
          </SelectTrigger>
          <SelectContent className='bg-white border border-[#E7E7E7] rounded-xl shadow-lg py-2 px-2'>
            <SelectItem
              value='marketplace'
              className='h-10 mb-1 last:mb-0 text-base'
            >
              For Marketplace Platforms
            </SelectItem>
            <SelectItem
              value='social'
              className='h-10 mb-1 last:mb-0 text-base'
            >
              For Social Media Platforms
            </SelectItem>
            <SelectItem value='ratio' className='h-10 mb-1 last:mb-0 text-base'>
              Ratio Size
            </SelectItem>
            <SelectItem
              value='custom'
              className='h-10 mb-1 last:mb-0 text-base'
            >
              Custom Size
            </SelectItem>
            <SelectItem
              value='original'
              className='h-10 mb-1 last:mb-0 text-base'
            >
              Original Size
            </SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* 预设列表 */}
      <div className='flex-1 overflow-y-auto px-4 pt-2'>
        {selectedCategory === 'original' ? (
          // Original Size 分类不显示任何内容
          <div className='space-y-4'>{/* 空白区域 */}</div>
        ) : selectedCategory === 'custom' ? (
          // Custom Size 分类直接显示控制选项
          <div className='space-y-4'>
            {/* Radio 选择 */}
            <div className='bg-white rounded-lg p-0'>
              <div className='flex bg-[#F5F5F5] rounded-lg p-1'>
                <button
                  onClick={() => setCustomMode('scale')}
                  className={cn(
                    'flex-1 py-2 px-3 text-sm font-medium rounded-md transition-colors',
                    customMode === 'scale'
                      ? 'bg-white text-[#121212] shadow-sm'
                      : 'text-[#878787] hover:text-[#121212]'
                  )}
                >
                  By Scale
                </button>
                <button
                  onClick={() => setCustomMode('dimensions')}
                  className={cn(
                    'flex-1 py-2 px-3 text-sm font-medium rounded-md transition-colors',
                    customMode === 'dimensions'
                      ? 'bg-white text-[#121212] shadow-sm'
                      : 'text-[#878787] hover:text-[#121212]'
                  )}
                >
                  By Dimensions
                </button>
              </div>
            </div>

            {/* By Scale 内容 */}
            {customMode === 'scale' && (
              <div className='space-y-3'>
                <div className='flex items-center gap-3'>
                  <Slider
                    value={[scaleValue]}
                    onValueChange={value => setScaleValue(value[0])}
                    min={20}
                    max={200}
                    step={1}
                    variant='brand'
                    className='flex-1'
                  />
                  <div className='text-sm font-medium text-[#121212] w-12 text-right'>
                    {scaleValue}%
                  </div>
                </div>
              </div>
            )}

            {/* By Dimensions 内容 */}
            {customMode === 'dimensions' && (
              <div className='space-y-3'>
                {/* 宽度和高度输入 */}
                <div className='flex items-center gap-1'>
                  {/* 宽度输入框 */}
                  <div className='flex-1'>
                    <div className='relative'>
                      <Input
                        type='number'
                        value={customWidth}
                        onChange={e => {
                          // 只允许输入数字和小数点
                          const value = e.target.value;
                          if (value === '' || /^\d*\.?\d*$/.test(value)) {
                            setCustomWidth(value);
                            let newHeight = customHeight;

                            // 如果锁定宽高比，自动计算高度
                            if (lockAspectRatio && value && images.length > 0) {
                              const firstImage = images[0];
                              if (firstImage.width && firstImage.height) {
                                const aspectRatio =
                                  firstImage.width / firstImage.height;
                                const calculatedHeight =
                                  parseFloat(value) / aspectRatio;
                                newHeight = calculatedHeight.toFixed(0);
                                setCustomHeight(newHeight);
                              }
                            }

                            // 实时验证 - 如果有错误则显示 tips
                            const widthNum = parseFloat(value);
                            const heightNum = parseFloat(newHeight);
                            if (
                              !isNaN(widthNum) &&
                              !isNaN(heightNum) &&
                              widthNum > 0 &&
                              heightNum > 0
                            ) {
                              const widthPx = convertToPixels(widthNum, unit);
                              const heightPx = convertToPixels(heightNum, unit);
                              const error = validateDimensions(
                                widthPx,
                                heightPx
                              );
                              if (error) {
                                showTips('error', error);
                              }
                            }
                          }
                        }}
                        placeholder='0'
                        className='h-10 pr-16 text-sm border-[#E7E7E7] rounded-lg'
                      />
                      <div className='absolute right-3 top-1/2 -translate-y-1/2 text-sm text-[#878787] pointer-events-none'>
                        Width
                      </div>
                    </div>
                  </div>

                  {/* 锁定按钮 */}
                  <button
                    onClick={() => setLockAspectRatio(!lockAspectRatio)}
                    className='w-6 h-6 flex items-center justify-center rounded border border-[#E7E7E7] hover:bg-gray-50 transition-colors'
                    title={
                      lockAspectRatio
                        ? 'Unlock Aspect Ratio'
                        : 'Lock Aspect Ratio'
                    }
                  >
                    {lockAspectRatio ? (
                      <Lock className='w-4 h-4 text-[#121212]' />
                    ) : (
                      <Unlock className='w-4 h-4 text-[#121212]' />
                    )}
                  </button>

                  {/* 高度输入框 */}
                  <div className='flex-1'>
                    <div className='relative'>
                      <Input
                        type='number'
                        value={customHeight}
                        onChange={e => {
                          // 只允许输入数字和小数点
                          const value = e.target.value;
                          if (value === '' || /^\d*\.?\d*$/.test(value)) {
                            setCustomHeight(value);
                            let newWidth = customWidth;

                            // 如果锁定宽高比，自动计算宽度
                            if (lockAspectRatio && value && images.length > 0) {
                              const firstImage = images[0];
                              if (firstImage.width && firstImage.height) {
                                const aspectRatio =
                                  firstImage.width / firstImage.height;
                                const calculatedWidth =
                                  parseFloat(value) * aspectRatio;
                                newWidth = calculatedWidth.toFixed(0);
                                setCustomWidth(newWidth);
                              }
                            }

                            // 实时验证 - 如果有错误则显示 tips
                            const widthNum = parseFloat(newWidth);
                            const heightNum = parseFloat(value);
                            if (
                              !isNaN(widthNum) &&
                              !isNaN(heightNum) &&
                              widthNum > 0 &&
                              heightNum > 0
                            ) {
                              const widthPx = convertToPixels(widthNum, unit);
                              const heightPx = convertToPixels(heightNum, unit);
                              const error = validateDimensions(
                                widthPx,
                                heightPx
                              );
                              if (error) {
                                showTips('error', error);
                              }
                            }
                          }
                        }}
                        placeholder='0'
                        className='h-10 pr-16 text-sm border-[#E7E7E7] rounded-lg'
                      />
                      <div className='absolute right-3 top-1/2 -translate-y-1/2 text-sm text-[#878787] pointer-events-none'>
                        Height
                      </div>
                    </div>
                  </div>
                </div>

                {/* 单位选择 */}
                <div>
                  <Select
                    value={unit}
                    onValueChange={(value: 'px' | 'in' | 'mm') => {
                      setUnit(value);
                      // 单位改变时重新验证
                      const widthNum = parseFloat(customWidth);
                      const heightNum = parseFloat(customHeight);
                      if (
                        !isNaN(widthNum) &&
                        !isNaN(heightNum) &&
                        widthNum > 0 &&
                        heightNum > 0
                      ) {
                        const widthPx = convertToPixels(widthNum, value);
                        const heightPx = convertToPixels(heightNum, value);
                        const error = validateDimensions(widthPx, heightPx);
                        if (error) {
                          showTips('error', error);
                        }
                      }
                    }}
                  >
                    <SelectTrigger className='w-full h-10 bg-white border border-[#E7E7E7] rounded-lg px-3'>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className='bg-white border border-[#E7E7E7] rounded-xl shadow-lg py-2 px-2'>
                      <SelectItem
                        value='px'
                        className='h-10 mb-1 last:mb-0 text-sm'
                      >
                        Pixel (px)
                      </SelectItem>
                      <SelectItem
                        value='in'
                        className='h-10 mb-1 last:mb-0 text-sm'
                      >
                        Inch (in)
                      </SelectItem>
                      <SelectItem
                        value='mm'
                        className='h-10 mb-1 last:mb-0 text-sm'
                      >
                        Millimeter (mm)
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            )}
          </div>
        ) : (
          // 其他分类显示预设列表
          <div className='space-y-2'>
            {RESIZE_PRESETS[selectedCategory].presets.map(preset => (
              <button
                key={preset.id}
                onClick={() => setSelectedPreset(preset.id)}
                className={cn(
                  'w-[312px] h-10 bg-white rounded-lg px-3 flex items-center gap-0.5 text-left transition-colors border',
                  selectedPreset === preset.id
                    ? 'bg-blue-50 border-blue-200'
                    : 'border-transparent hover:bg-gray-100'
                )}
              >
                <div className='flex items-center gap-2 w-[288px]'>
                  <div className='w-7 h-7 bg-white border border-[#E7E7E7] rounded flex items-center justify-center overflow-hidden'>
                    {preset.icon.startsWith('/') ? (
                      <Image
                        src={preset.icon}
                        alt={preset.name}
                        width={28}
                        height={28}
                        className='object-contain w-full h-full'
                      />
                    ) : (
                      <div className='text-xs'>{preset.icon}</div>
                    )}
                  </div>
                  <div className='flex items-center justify-between flex-1'>
                    <div className='text-sm font-normal text-[#000000] w-40'>
                      {preset.name}
                    </div>
                    <div className='text-sm font-normal text-[#878787] w-[84px] text-right'>
                      {preset.mode === 'fixed'
                        ? `${(preset as FixedPreset).width}x${(preset as FixedPreset).height}`
                        : preset.mode === 'ratio'
                          ? 'Ratio'
                          : 'Custom'}
                    </div>
                  </div>
                </div>
              </button>
            ))}
          </div>
        )}
      </div>

      {/* 底部应用按钮 */}
      <div className='px-4 py-6 bg-white rounded-b-2xl'>
        <Button
          onClick={handleApply}
          disabled={shouldDisableBatchApplyButton(isProcessing, images)}
          className='w-full h-12 bg-[#FFCC03] hover:bg-[#FFCC03]/90 text-[#121212] text-base font-medium rounded-xl disabled:opacity-50'
          size='lg'
        >
          {isProcessing ? (
            <div className='flex items-center gap-2'>
              <Image
                src='/apps/icons/loading.png'
                alt='loading'
                width={16}
                height={16}
                className='animate-spin'
              />
              Processing...
            </div>
          ) : (
            'Apply'
          )}
        </Button>
      </div>
    </div>
  );
}
