import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { getAccessToken } from '@/lib/cache';
import {
  getUserInfo as apiGetUserInfo,
  type UserInfo as ApiUserInfo,
} from '@/api/auth';

// 会员等级枚举
export enum MemberLevel {
  Free = 1,
  Basic = 2,
  Pro = 3,
  Unlimited = 4,
}

// 扩展的用户信息接口，包含业务相关字段
export interface UserInfo extends ApiUserInfo {
  account_id?: number; // 账号体系id
  head_pic?: string; // 头像
  member_level?: MemberLevel; // 会员等级(1=Free,2=Basic,3=pro,4=Unlimited)
  is_vip?: number; // 是否vip
  score?: number; // 剩余点数
  total_score?: string; // 总点数
  batch_process_limit?: number; // 批量下载次数
}

// 认证状态
export interface AuthState {
  isAuthenticated: boolean;
  isLoading: boolean;
  userInfo: UserInfo;
  // 用户行为跟踪
  hasPerformedBackgroundRemoval: boolean;
  lastDailyVisit: string | null; // 格式: YYYY-MM-DD
}

// 认证操作
export interface AuthActions {
  // 用户信息管理
  updateUserInfo: (userInfo: UserInfo) => void;

  // 认证状态管理
  setAuthenticated: (isAuthenticated: boolean) => void;
  setLoading: (isLoading: boolean) => void;

  // 认证检查
  checkAuth: () => Promise<boolean>;

  // 初始化
  initialize: () => Promise<void>;

  // 用户行为跟踪
  markBackgroundRemovalPerformed: () => void;
  checkAndUpdateDailyVisit: () => boolean; // 返回是否是今日首次访问
}

type AuthStore = AuthState & AuthActions;

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // 初始状态
      isAuthenticated: true, // FIXME 临时设置为true，避免每次加载都检查token,上线前删除
      isLoading: false,
      userInfo: {
        id: '',
        username: '',
        account_id: 0,
        email: '',
        head_pic: '',
        member_level: MemberLevel.Free,
        is_vip: 0,
        score: 0,
        total_score: '0',
        batch_process_limit: 0,
      },
      // 用户行为跟踪初始状态
      hasPerformedBackgroundRemoval: false,
      lastDailyVisit: null,

      // 用户信息管理
      updateUserInfo: (userInfo: UserInfo) => {
        set({ userInfo });
      },

      // 认证状态管理
      setAuthenticated: (isAuthenticated: boolean) => {
        set({ isAuthenticated });
      },

      setLoading: (isLoading: boolean) => {
        set({ isLoading });
      },

      // 检查认证状态
      checkAuth: async (): Promise<boolean> => {
        try {
          const token = getAccessToken();
          if (!token) {
            set({ isAuthenticated: false });
            return false;
          }

          // 尝试获取用户信息来验证token有效性
          const userInfo = await apiGetUserInfo();
          set({ userInfo, isAuthenticated: true });
          return true;
        } catch (error) {
          console.error('认证检查失败:', error);
          set({ isAuthenticated: false });
          return false;
        }
      },

      // 初始化认证状态
      initialize: async () => {
        try {
          set({ isLoading: true });

          const token = getAccessToken();
          if (token) {
            await get().checkAuth();
          } else {
            set({ isAuthenticated: false });
          }

          set({ isLoading: false });
        } catch (error) {
          console.error('认证初始化失败:', error);
          set({ isLoading: false, isAuthenticated: false });
        }
      },

      // 用户行为跟踪方法
      markBackgroundRemovalPerformed: () => {
        set({ hasPerformedBackgroundRemoval: true });
      },

      checkAndUpdateDailyVisit: () => {
        const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD格式
        const state = get();

        if (state.lastDailyVisit !== today) {
          // 新的一天，重置状态
          set({
            lastDailyVisit: today,
            hasPerformedBackgroundRemoval: false,
          });
          return true; // 是今日首次访问
        }

        return false; // 不是今日首次访问
      },
    }),
    {
      name: 'auth-storage',
      // 只持久化必要的状态
      partialize: state => ({
        userInfo: state.userInfo,
        isAuthenticated: state.isAuthenticated,
        hasPerformedBackgroundRemoval: state.hasPerformedBackgroundRemoval,
        lastDailyVisit: state.lastDailyVisit,
      }),
    }
  )
);

// 保持向后兼容的导出
export const accountInfoStore = useAuthStore;
