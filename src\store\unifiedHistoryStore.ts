'use client';

import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import type { ImageState } from './imageStore';

// ==================== 类型定义 ====================

/**
 * 操作类型枚举
 */
export type OperationType =
  | 'upload' // 上传图片
  | 'background' // 背景处理（去除/更换）
  | 'resize' // 尺寸调整
  | 'compress' // 压缩
  | 'convert' // 格式转换
  | 'rename' // 重命名
  | 'erase' // 橡皮擦
  | 'batch_background' // 批量背景处理
  | 'batch_resize' // 批量尺寸调整
  | 'batch_compress' // 批量压缩
  | 'batch_convert' // 批量格式转换
  | 'batch_rename'; // 批量重命名

/**
 * 图片状态快照
 */
export interface ImageSnapshot {
  id: string;
  name: string;
  width: number;
  height: number;
  size: number;
  status: ImageState['status'];

  // 背景相关
  backgroundColor?: string;
  backgroundImageUrl?: string;
  backgroundImageId?: string;
  processedUrl?: string | null;

  // 尺寸相关
  targetWidth?: number;
  targetHeight?: number;
  originalWidth?: number;
  originalHeight?: number;
  resizedUrl?: string | null;

  // 压缩相关
  originalSize?: number;
  compressedSize?: number;
  compressedUrl?: string | null;
  compressionLevel?: 'original' | 'light' | 'medium' | 'deep';

  // 格式转换相关
  originalFormat?: string;
  convertedFormat?: string;
  convertedUrl?: string | null;

  // 预览相关
  compositePreviewUrl?: string | null;
}

/**
 * 历史记录项
 */
export interface HistoryItem {
  id: string;
  timestamp: number;
  operation: OperationType;
  description: string;
  affectedImageIds: string[];
  beforeStates: Record<string, ImageSnapshot>;
  afterStates: Record<string, ImageSnapshot>;
  metadata?: Record<string, unknown>;
}

/**
 * 历史记录状态
 */
interface HistoryState {
  history: HistoryItem[];
  currentIndex: number; // -1 表示在最新状态，0 表示在第一个历史记录
  maxHistorySize: number;
  isEnabled: boolean;
}

/**
 * 历史记录操作
 */
interface HistoryActions {
  // 记录操作
  record: (item: Omit<HistoryItem, 'id' | 'timestamp'>) => void;

  // 撤销/重做
  undo: () => HistoryItem | null;
  redo: () => HistoryItem | null;

  // 管理
  clear: () => void;
  setEnabled: (enabled: boolean) => void;
  setMaxHistorySize: (size: number) => void;

  // 工具方法
  createImageSnapshot: (
    imageId: string,
    imageData: ImageState
  ) => ImageSnapshot;
  getHistoryLength: () => number;
}

// ==================== Store 实现 ====================

/**
 * 统一历史记录 Store
 */
export const useUnifiedHistoryStore = create<HistoryState & HistoryActions>()(
  immer((set, get) => ({
    // 初始状态
    history: [],
    currentIndex: -1,
    maxHistorySize: 50,
    isEnabled: true,

    // ==================== 核心操作 ====================

    /**
     * 记录新的操作
     */
    record: item => {
      console.log('🔍 [unifiedHistoryStore.record] 开始记录:', {
        operation: item.operation,
        description: item.description,
        affectedImages: item.affectedImageIds.length,
      });

      const state = get();
      if (!state.isEnabled) {
        console.log('🔍 [unifiedHistoryStore.record] 历史记录已禁用，跳过记录');
        return;
      }

      set(draft => {
        const newItem: HistoryItem = {
          ...item,
          id: `history_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
          timestamp: Date.now(),
        };

        // 检查是否与最后一个操作相同（去重逻辑）
        const lastItem = draft.history[draft.history.length - 1];
        if (lastItem && draft.currentIndex === -1) {
          // 检查操作类型、描述和影响的图片是否相同
          const isSameOperation =
            lastItem.operation === newItem.operation &&
            lastItem.description === newItem.description &&
            lastItem.affectedImageIds.length ===
              newItem.affectedImageIds.length &&
            lastItem.affectedImageIds.every(
              (id, index) => id === newItem.affectedImageIds[index]
            );

          if (isSameOperation) {
            // 检查状态是否真的有变化
            const hasStateChange = Object.keys(newItem.afterStates).some(
              imageId => {
                const oldState = lastItem.afterStates[imageId];
                const newState = newItem.afterStates[imageId];
                if (!oldState || !newState) return true;

                // 比较关键属性是否有变化
                return (
                  oldState.name !== newState.name ||
                  oldState.backgroundColor !== newState.backgroundColor ||
                  oldState.backgroundImageUrl !== newState.backgroundImageUrl ||
                  oldState.targetWidth !== newState.targetWidth ||
                  oldState.targetHeight !== newState.targetHeight ||
                  oldState.convertedFormat !== newState.convertedFormat ||
                  oldState.compressionLevel !== newState.compressionLevel
                );
              }
            );

            if (!hasStateChange) {
              console.log('🔄 跳过重复操作记录:', newItem.description);
              return;
            }
          }
        }

        // 如果当前不在最新状态，清除后续的历史记录
        if (draft.currentIndex >= 0) {
          draft.history = draft.history.slice(0, draft.currentIndex + 1);
        }

        // 添加新的历史记录
        draft.history.push(newItem);

        // 限制历史记录数量
        if (draft.history.length > draft.maxHistorySize) {
          const removeCount = draft.history.length - draft.maxHistorySize;
          draft.history.splice(0, removeCount);
        }

        // 重置到最新状态
        draft.currentIndex = -1;

        console.log('📝 [unifiedHistoryStore.record] 已记录历史操作:', {
          operation: newItem.operation,
          description: newItem.description,
          affectedImages: newItem.affectedImageIds.length,
          historyLength: draft.history.length,
        });
      });
    },

    /**
     * 撤销操作
     */
    undo: () => {
      const state = get();
      const canUndo =
        state.history.length > 0 &&
        (state.currentIndex > 0 || state.currentIndex === -1);

      if (!canUndo) {
        console.warn('无法撤销：没有可撤销的操作');
        return null;
      }

      let targetItem: HistoryItem | null = null;

      set(draft => {
        if (draft.currentIndex === -1) {
          // 从最新状态撤销到最后一个历史记录
          draft.currentIndex = draft.history.length - 1;
          targetItem = draft.history[draft.currentIndex];
        } else if (draft.currentIndex > 0) {
          // 撤销到前一个历史记录
          draft.currentIndex--;
          targetItem = draft.history[draft.currentIndex];
        }

        console.log('🔙 撤销操作:', {
          newIndex: draft.currentIndex,
          targetOperation: targetItem?.operation,
          targetDescription: targetItem?.description,
        });
      });

      return targetItem;
    },

    /**
     * 重做操作
     */
    redo: () => {
      const state = get();

      // 检查是否可以重做
      // currentIndex >= 0: 不在最新状态
      // currentIndex < history.length - 1: 不在最后一个历史记录
      const canRedo =
        state.currentIndex >= 0 &&
        state.currentIndex < state.history.length - 1;

      if (!canRedo) {
        console.warn('无法重做：没有可重做的操作', {
          currentIndex: state.currentIndex,
          historyLength: state.history.length,
        });
        return null;
      }

      let targetItem: HistoryItem | null = null;

      set(draft => {
        // 重做到下一个历史记录
        draft.currentIndex++;
        targetItem = draft.history[draft.currentIndex];

        console.log('🔜 重做操作:', {
          newIndex: draft.currentIndex,
          targetOperation: targetItem?.operation,
          targetDescription: targetItem?.description,
          isAtLatest: draft.currentIndex === draft.history.length - 1,
        });
      });

      return targetItem;
    },

    // ==================== 管理功能 ====================

    /**
     * 清空历史记录
     */
    clear: () => {
      set(draft => {
        draft.history = [];
        draft.currentIndex = -1;
      });
      console.log('🗑️ 历史记录已清空');
    },

    /**
     * 设置启用状态
     */
    setEnabled: enabled => {
      set(draft => {
        draft.isEnabled = enabled;
      });
      console.log(`📋 历史记录${enabled ? '已启用' : '已禁用'}`);
    },

    /**
     * 设置最大历史记录数量
     */
    setMaxHistorySize: size => {
      set(draft => {
        draft.maxHistorySize = Math.max(1, size);

        // 如果当前历史记录超过新的限制，删除最旧的记录
        if (draft.history.length > draft.maxHistorySize) {
          const removeCount = draft.history.length - draft.maxHistorySize;
          draft.history.splice(0, removeCount);

          // 调整当前索引
          if (draft.currentIndex >= 0) {
            draft.currentIndex = Math.max(0, draft.currentIndex - removeCount);
          }
        }
      });
    },

    // ==================== 工具方法 ====================

    /**
     * 创建图片状态快照
     */
    createImageSnapshot: (imageId, imageData) => {
      return {
        id: imageId,
        name: imageData.name,
        width: imageData.width,
        height: imageData.height,
        size: imageData.size,
        status: imageData.status,

        // 背景相关
        backgroundColor: imageData.backgroundColor,
        backgroundImageUrl: imageData.backgroundImageUrl,
        backgroundImageId: imageData.backgroundImageId,
        processedUrl: imageData.processedUrl,

        // 尺寸相关
        targetWidth: imageData.targetWidth,
        targetHeight: imageData.targetHeight,
        originalWidth: imageData.originalWidth,
        originalHeight: imageData.originalHeight,
        resizedUrl: imageData.resizedUrl,

        // 压缩相关
        originalSize: imageData.originalSize,
        compressedSize: imageData.compressedSize,
        compressedUrl: imageData.compressedUrl,
        compressionLevel: imageData.compressionLevel,

        // 格式转换相关
        originalFormat: imageData.originalFormat,
        convertedFormat: imageData.convertedFormat,
        convertedUrl: imageData.convertedUrl,

        // 预览相关
        compositePreviewUrl: imageData.compositePreviewUrl,
      };
    },

    /**
     * 获取历史记录长度
     */
    getHistoryLength: () => {
      return get().history.length;
    },
  }))
);

// ==================== 导出便捷操作 ====================

/**
 * 历史记录操作对象
 */
export const historyActions = {
  record: (item: Omit<HistoryItem, 'id' | 'timestamp'>) =>
    useUnifiedHistoryStore.getState().record(item),

  undo: () => useUnifiedHistoryStore.getState().undo(),
  redo: () => useUnifiedHistoryStore.getState().redo(),
  clear: () => useUnifiedHistoryStore.getState().clear(),
  setEnabled: (enabled: boolean) =>
    useUnifiedHistoryStore.getState().setEnabled(enabled),
  setMaxHistorySize: (size: number) =>
    useUnifiedHistoryStore.getState().setMaxHistorySize(size),
};

// ==================== 选择器 ====================

/**
 * 获取历史记录状态的选择器
 */
export const selectHistoryState = () => {
  const state = useUnifiedHistoryStore.getState();
  return {
    canUndo:
      state.history.length > 0 &&
      (state.currentIndex > 0 || state.currentIndex === -1),
    canRedo:
      state.currentIndex >= 0 && state.currentIndex < state.history.length - 1,
    history: state.history,
    currentIndex: state.currentIndex,
    isEnabled: state.isEnabled,
    historyLength: state.history.length,
  };
};
