import { removeAccessToken, removeRedirectHref } from '@/lib/cache';

/**
 * 获取当前页面URL（用于redirect参数）
 */
function getCurrentPageUrl(): string {
  if (typeof window === 'undefined') return '';
  return window.location.href;
}

/**
 * 构建带redirect参数的URL
 */
function buildRedirectUrl(baseUrl: string, redirectUrl?: string): string {
  const redirect = redirectUrl || getCurrentPageUrl();
  return `${baseUrl}?redirect=${redirect}`;
}

/**
 * 跳转到登录页面
 */
export function redirectToLogin(redirectUrl?: string): void {
  // 清理重定向
  removeRedirectHref();

  const loginUrl = process.env.NEXT_PUBLIC_LOGIN_URL;
  if (loginUrl) {
    window.location.href = buildRedirectUrl(loginUrl, redirectUrl);
  }
}

/**
 * 跳转到注册页面
 */
export function redirectToRegister(redirectUrl?: string): void {
  // 清理重定向
  removeRedirectHref();

  const registerUrl = process.env.NEXT_PUBLIC_REGISTER_URL;
  if (registerUrl) {
    window.location.href = buildRedirectUrl(registerUrl, redirectUrl);
  }
}

/**
 * 跳转到账户中心
 */
export function redirectToAccountCenter(): void {
  // 清理重定向
  removeRedirectHref();

  const accountUrl = process.env.NEXT_PUBLIC_ACCOUNT_URL;
  if (accountUrl) {
    window.location.href = accountUrl;
  }
}

/**
 * 登出功能
 * 1. 清理本地token
 * 2. 重定向到登录页面（带redirect参数）
 */
export function logout(redirectUrl?: string): void {
  // 清理本地token
  removeAccessToken();

  // 清理重定向
  removeRedirectHref();

  // 重定向到登录页面
  redirectToLogin(redirectUrl);
}

/**
 * 认证相关的统一跳转工具
 */
export const authUtils = {
  login: redirectToLogin,
  register: redirectToRegister,
  accountCenter: redirectToAccountCenter,
  logout,
  getCurrentPageUrl,
  buildRedirectUrl,
};
