# 路由结构改造说明

## 改造背景
项目需要部署到 `https://pixpretty-test.tenorshare.ai/apps/` 域名下，运维已将项目指向域名下的 `/apps` 二级路由。

## 方案对比与最终选择

### 🎯 最终选择：basePath 配置方案

经过实际部署需求分析，我们选择使用 Next.js 的 `basePath` 配置：

| 对比维度 | basePath 方案 | 文件夹方案 |
|----------|---------------|------------|
| **部署适配** | ✅ 完美适配运维要求 | ❌ 需要额外代理配置 |
| **配置简单** | ✅ Next.js 原生支持 | ❌ 需要复杂路径映射 |
| **资源路径** | ✅ 自动处理静态资源 | ❌ 需要手动配置 |
| **开发部署一致** | ✅ 开发生产路径一致 | ❌ 开发生产路径不同 |
| **运维友好** | ✅ 直接部署到子路径 | ❌ 需要反向代理 |

### 选择理由
1. **运维需求匹配**：运维已将项目直接指向 `/apps` 路径，basePath 完美适配
2. **配置简洁**：Next.js 原生支持，配置简单可靠
3. **资源处理**：自动处理 `_next` 静态资源路径
4. **部署一致性**：开发和生产环境路径行为一致

## 当前配置

### 1. Next.js 配置
```typescript
// next.config.ts
const nextConfig: NextConfig = {
  basePath: '/apps',
  assetPrefix: '/apps',
};
```

### 2. 目录结构
```
src/app/
├── page.tsx                    # 根页面 → 重定向到 /remove-background
├── remove-background/
│   └── page.tsx               # 去除背景功能页面
├── batch-editor/
│   └── page.tsx               # 批量编辑功能页面
└── api/
    └── healthz/
        └── route.ts           # 健康检查接口
```

### 3. 路由映射

| 文件路径 | 开发环境访问 | 生产环境访问 | 内容 |
|----------|-------------|-------------|------|
| `src/app/page.tsx` | `http://localhost:3000/apps/` | `https://domain.com/apps/` | 重定向到 `/apps/remove-background` |
| `src/app/remove-background/page.tsx` | `http://localhost:3000/apps/remove-background` | `https://domain.com/apps/remove-background` | 去除背景功能页面 |
| `src/app/batch-editor/page.tsx` | `http://localhost:3000/apps/batch-editor` | `https://domain.com/apps/batch-editor` | 批量编辑功能页面 |

## 技术实现

### 重定向策略
```tsx
// src/app/page.tsx - 根页面重定向
'use client';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

export default function RootPage() {
  const router = useRouter();
  
  useEffect(() => {
    // basePath 环境下的重定向
    router.replace('/remove-background');
  }, [router]);
  
  return <div>Loading...</div>;
}
```

### 功能页面组件
```tsx
// src/app/remove-background/page.tsx
'use client';
import { DesktopPage } from '@/components/desktop-page';
import { MobilePage } from '@/components/mobile-page';
import { useDevice } from '@/lib/context/device-context';

export default function RemoveBackground() {
  const { isMobile } = useDevice();
  return isMobile ? <MobilePage /> : <DesktopPage />;
}
```

## 部署配置

### 当前部署方式
- **配置**: 启用 `basePath: '/apps'` 和 `assetPrefix: '/apps'`
- **适用场景**: 运维直接将项目部署到 `/apps` 子路径
- **访问地址**: `https://pixpretty-test.tenorshare.ai/apps/`

### 本地开发
```bash
npm run dev
```

**开发环境访问路径**：
- `http://localhost:3000/apps/` → 重定向到 `/apps/remove-background`
- `http://localhost:3000/apps/remove-background` → 去除背景功能
- `http://localhost:3000/apps/batch-editor` → 批量编辑功能

### 生产环境
**目标访问路径**：
- `https://pixpretty-test.tenorshare.ai/apps/` → 重定向到去除背景
- `https://pixpretty-test.tenorshare.ai/apps/remove-background` → 去除背景功能
- `https://pixpretty-test.tenorshare.ai/apps/batch-editor` → 批量编辑功能

## 静态资源处理

### 自动路径调整
basePath 配置会自动处理：
- `_next/static/*` → `/apps/_next/static/*`
- `favicon.ico` → `/apps/favicon.ico`
- 图片和其他公共资源 → `/apps/[resource]`

### 内部链接使用
```tsx
// 推荐的内部导航方式
import { useRouter } from 'next/navigation';

const router = useRouter();
router.push('/remove-background');    // 会自动加上 /apps 前缀
router.push('/batch-editor');         // 会自动加上 /apps 前缀
```

## 后续开发指南

### 添加新功能页面
```bash
# 在 app 目录下创建新功能
src/app/new-feature/page.tsx
# 访问路径将是: /apps/new-feature
```

### API 路由
```bash
# API 路由也会自动添加 basePath
src/app/api/upload/route.ts
# 访问路径: /apps/api/upload
```

### 注意事项
1. **开发环境**: 必须访问 `http://localhost:3000/apps/` 而不是根路径
2. **内部跳转**: 使用相对路径，Next.js 会自动处理 basePath
3. **API 调用**: 所有 API 路径都会自动添加 `/apps` 前缀
4. **外部链接**: 如需链接到根域名其他路径，使用绝对路径

## 优势总结

✅ **部署适配**: 完美匹配运维的子路径部署需求  
✅ **配置简单**: Next.js 原生支持，配置可靠
✅ **资源自动**: 自动处理所有静态资源路径
✅ **开发一致**: 开发和生产环境行为一致
✅ **维护简单**: 标准 Next.js 配置，易于维护 