'use client';

import { useEffect } from 'react';
import { useAuthStore } from '@/store/accountStore';
import Cookies from 'js-cookie';

interface AuthProviderProps {
  children: React.ReactNode;
}

/**
 * 认证提供者组件
 * 专门负责初始化认证系统，不包含UI逻辑
 * 职责：
 * - 应用启动时初始化认证状态
 * - 检查token有效性并更新用户信息
 * - 为整个应用提供认证上下文
 */
export function AuthProvider({ children }: AuthProviderProps) {
  const { initialize } = useAuthStore();

  // 开发环境手动设置token
  // FIXME 上线前删除
  if (process.env.NODE_ENV == 'development') {
    Cookies.set(
      'access_token',
      'eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************.kgqebDcdIFKQiBsfRTKXNPfNzjeY3ZM7LCiUCnAL6yhrLnCC-TMJb2OZzbW0MhTy5acatGFpbimOltBAWO6ONg',
      {
        path: '/',
        expires: 15,
      }
    );
  }

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        await initialize();
      } catch (error) {
        console.error('认证初始化失败:', error);
      }
    };

    initializeAuth();
  }, [initialize]);

  // 直接渲染子组件，不包装AuthGuard
  // AuthGuard应该在需要认证保护的具体位置使用
  return <>{children}</>;
}
