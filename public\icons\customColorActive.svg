<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_18_2350)">
<mask id="mask0_18_2350" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="32" height="32">
<circle cx="16" cy="16" r="16" fill="#D9D9D9"/>
</mask>
<g mask="url(#mask0_18_2350)">
<g filter="url(#filter0_f_18_2350)">
<circle cx="4" cy="-4" r="16" fill="#43FF70"/>
</g>
<g filter="url(#filter1_f_18_2350)">
<circle cx="28" cy="-4" r="16" fill="#FFF500"/>
</g>
<g filter="url(#filter2_f_18_2350)">
<circle cx="38" cy="14" r="16" fill="#FF3737"/>
</g>
<g filter="url(#filter3_f_18_2350)">
<circle cx="32" cy="30" r="16" fill="#DC34D9"/>
</g>
<g filter="url(#filter4_f_18_2350)">
<circle cx="10" cy="36" r="16" fill="#4E52FB"/>
</g>
<g filter="url(#filter5_f_18_2350)">
<circle cx="-6" cy="18" r="16" fill="#57FBFD"/>
</g>
<circle cx="16" cy="16" r="15" stroke="black" stroke-width="2" style="mix-blend-mode:soft-light"/>
<g filter="url(#filter6_f_18_2350)">
<circle cx="16" cy="16" r="6" fill="url(#paint0_radial_18_2350)"/>
</g>
<g filter="url(#filter7_d_18_2350)">
<path d="M16 8C16.5523 8 17 8.44772 17 9V15H23C23.5523 15 24 15.4477 24 16C24 16.5523 23.5523 17 23 17H17V23C17 23.5523 16.5523 24 16 24C15.4477 24 15 23.5523 15 23V17H9C8.44772 17 8 16.5523 8 16C8 15.4477 8.44772 15 9 15H15V9C15 8.44772 15.4477 8 16 8Z" fill="white"/>
</g>
<circle cx="16" cy="16" r="13.5" stroke="white"/>
</g>
</g>
<rect x="1" y="1" width="30" height="30" rx="15" stroke="#FFCC03" stroke-width="2"/>
<defs>
<filter id="filter0_f_18_2350" x="-21.2" y="-29.2" width="50.4" height="50.4" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="4.6" result="effect1_foregroundBlur_18_2350"/>
</filter>
<filter id="filter1_f_18_2350" x="2.8" y="-29.2" width="50.4" height="50.4" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="4.6" result="effect1_foregroundBlur_18_2350"/>
</filter>
<filter id="filter2_f_18_2350" x="12.8" y="-11.2" width="50.4" height="50.4" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="4.6" result="effect1_foregroundBlur_18_2350"/>
</filter>
<filter id="filter3_f_18_2350" x="6.8" y="4.8" width="50.4" height="50.4" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="4.6" result="effect1_foregroundBlur_18_2350"/>
</filter>
<filter id="filter4_f_18_2350" x="-15.2" y="10.8" width="50.4" height="50.4" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="4.6" result="effect1_foregroundBlur_18_2350"/>
</filter>
<filter id="filter5_f_18_2350" x="-31.2" y="-7.2" width="50.4" height="50.4" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="4.6" result="effect1_foregroundBlur_18_2350"/>
</filter>
<filter id="filter6_f_18_2350" x="0" y="0" width="32" height="32" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="5" result="effect1_foregroundBlur_18_2350"/>
</filter>
<filter id="filter7_d_18_2350" x="0" y="2" width="32" height="32" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0705882 0 0 0 0 0.0705882 0 0 0 0 0.0705882 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_18_2350"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_18_2350" result="shape"/>
</filter>
<radialGradient id="paint0_radial_18_2350" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(16 16) rotate(90) scale(6)">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</radialGradient>
<clipPath id="clip0_18_2350">
<rect width="32" height="32" rx="16" fill="white"/>
</clipPath>
</defs>
</svg>
