'use client';

import Image from 'next/image';
import {
  createContext,
  ReactNode,
  useCallback,
  useContext,
  useState,
} from 'react';

// Tips类型
export type TipsType = 'success' | 'error' | 'warning' | 'info';

// Tips参数
export interface ShowTipsParams {
  type: TipsType;
  message: string;
  duration?: number;
}

// Context类型
interface TipsContextType {
  showTips: (type: TipsType, message: string, duration?: number) => void;
}

const TipsContext = createContext<TipsContextType | undefined>(undefined);

// 图标组件
const TipsIcon = ({ type }: { type: TipsType }) => {
  const getIconPath = (iconType: TipsType) => {
    return `/apps/icons/${iconType}.svg`;
  };

  return (
    <div className='relative size-6 flex items-center justify-center'>
      <Image
        width={22}
        height={22}
        src={getIconPath(type)}
        alt={`${type} icon`}
      />
    </div>
  );
};

// Tips显示组件
const TipsDisplay = ({
  type,
  message,
}: {
  type: TipsType;
  message: string;
}) => {
  return (
    <div className='fixed top-4 left-1/2 transform -translate-x-1/2 z-[9999] animate-in fade-in slide-in-from-top-2 duration-300 px-4'>
      <div className='bg-[rgba(18,18,18,0.6)] backdrop-blur-sm rounded-2xl px-4 py-2 flex items-center gap-2 max-w-[90vw] md:max-w-md whitespace-nowrap'>
        <TipsIcon type={type} />
        <span className='text-white text-[14px] font-normal leading-[1.5]'>
          {message}
        </span>
      </div>
    </div>
  );
};

// Provider组件
export const TipsProvider = ({ children }: { children: ReactNode }) => {
  const [currentTips, setCurrentTips] = useState<{
    type: TipsType;
    message: string;
    id: number;
  } | null>(null);

  const showTips = useCallback(
    (type: TipsType, message: string, duration: number = 3000) => {
      const id = Date.now();

      // 清除之前的tips
      setCurrentTips({ type, message, id });

      // 自动关闭
      setTimeout(() => {
        setCurrentTips(prev => (prev?.id === id ? null : prev));
      }, duration);
    },
    []
  );

  return (
    <TipsContext.Provider value={{ showTips }}>
      {children}
      {currentTips && (
        <TipsDisplay type={currentTips.type} message={currentTips.message} />
      )}
    </TipsContext.Provider>
  );
};

// Hook
export const useTips = () => {
  const context = useContext(TipsContext);
  if (!context) {
    throw new Error('useTips must be used within a TipsProvider');
  }
  return context;
};
