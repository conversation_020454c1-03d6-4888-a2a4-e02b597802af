'use client';

import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

/**
 * 根页面 - 重定向到去除背景应用
 * 用户访问根路径时自动跳转到 /remove-background
 */
export default function RootPage() {
  const router = useRouter();

  useEffect(() => {
    // 重定向到去除背景页面
    router.replace('/remove-background');
  }, [router]);

  return (
    <div className='min-h-screen flex items-center justify-center'>
      <div className='text-center'>
        <div className='animate-spin mx-auto w-12 h-12'>
          <Image
            src='/apps/icons/loading.png'
            alt='loading'
            width={48}
            height={48}
          />
        </div>
      </div>
    </div>
  );
}
