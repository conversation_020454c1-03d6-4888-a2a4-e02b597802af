# 统一图片处理管道 - 快速开始指南

## 概述

统一图片处理管道是一个强大的图片处理系统，能够将多种操作（背景处理、尺寸调整、压缩、格式转换）组合应用，确保预览和下载的一致性。

## 5分钟快速集成

### 1. 基本导入

```typescript
import { 
  processImagePipeline, 
  generatePreviewUrl,
  type ImageProcessingConfig 
} from '@/lib/utils/imageProcessingPipeline';
import { getImageDownloadInfo } from '@/lib/utils/imageDownload';
```

### 2. 简单使用示例

```typescript
// 基本图片处理
const config: ImageProcessingConfig = {
  backgroundColor: '#ffffff',
  targetWidth: 400,
  targetHeight: 300,
  resizeMode: 'fit',
  outputFormat: 'jpg',
  compressionLevel: 'medium'
};

// 处理图片
const result = await processImagePipeline(
  'blob:http://localhost:3000/original-image',
  null, // 没有去背图片
  config
);

console.log('处理完成:', result);
// 输出: { dataUrl: 'data:image/jpeg;base64,...', width: 400, height: 300, ... }
```

### 3. 集成到现有组件

```typescript
// 在React组件中使用
const MyImageProcessor = () => {
  const [processedImage, setProcessedImage] = useState<string>('');
  
  const handleProcess = async (imageUrl: string) => {
    try {
      const result = await processImagePipeline(imageUrl, null, {
        backgroundColor: '#ff0000',
        targetWidth: 300,
        targetHeight: 200,
        resizeMode: 'fit',
        outputFormat: 'png'
      });
      
      setProcessedImage(result.dataUrl);
    } catch (error) {
      console.error('处理失败:', error);
    }
  };
  
  return (
    <div>
      {processedImage && (
        <img src={processedImage} alt="处理后的图片" />
      )}
    </div>
  );
};
```

## 常见使用场景

### 场景1: 批量背景替换

```typescript
// 批量设置背景颜色
const applyBackgroundToAll = async (images: ImageState[], backgroundColor: string) => {
  // 1. 更新图片状态
  useImageStore.setState(state => {
    images.forEach(image => {
      const existingImage = state.images.get(image.id);
      if (existingImage) {
        existingImage.backgroundColor = backgroundColor;
      }
    });
  });
  
  // 2. 批量更新预览
  const imageIds = images.map(img => img.id);
  await useImageStore.getState().batchUpdatePreviewUrls(imageIds);
  
  console.log(`已为 ${images.length} 张图片设置背景颜色: ${backgroundColor}`);
};
```

### 场景2: 批量尺寸调整

```typescript
// 批量调整图片尺寸
const resizeAllImages = async (images: ImageState[], width: number, height: number) => {
  for (const image of images) {
    try {
      // 使用现有的批量尺寸调整功能
      await useImageStore.getState().batchResizeImages(
        [image.id], 
        width, 
        height, 
        'fit'
      );
      
      console.log(`图片 ${image.name} 尺寸调整完成`);
    } catch (error) {
      console.error(`图片 ${image.name} 尺寸调整失败:`, error);
    }
  }
};
```

### 场景3: 组合操作下载

```typescript
// 下载经过多种操作的图片
const downloadProcessedImage = async (imageState: ImageState) => {
  try {
    // 使用统一下载逻辑
    const downloadInfo = await getImageDownloadInfo(imageState);
    
    // 创建下载链接
    const link = document.createElement('a');
    link.href = downloadInfo.url;
    link.download = downloadInfo.fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    console.log(`图片 ${downloadInfo.fileName} 下载完成`);
  } catch (error) {
    console.error('下载失败:', error);
  }
};
```

### 场景4: 实时预览更新

```typescript
// 实时更新预览（用于UI组件）
const ImagePreview = ({ image }: { image: ImageState }) => {
  const [previewUrl, setPreviewUrl] = useState<string>(image.previewUrl);
  
  useEffect(() => {
    const updatePreview = async () => {
      try {
        const newPreviewUrl = await generatePreviewUrl(
          image.previewUrl,
          image.processedUrl,
          {
            backgroundColor: image.backgroundColor,
            backgroundImageUrl: image.backgroundImageUrl,
            targetWidth: image.targetWidth,
            targetHeight: image.targetHeight,
            resizeMode: image.resizeMode
          }
        );
        setPreviewUrl(newPreviewUrl);
      } catch (error) {
        console.error('预览更新失败:', error);
        // 回退到原始预览
        setPreviewUrl(image.processedUrl || image.previewUrl);
      }
    };
    
    updatePreview();
  }, [
    image.backgroundColor, 
    image.backgroundImageUrl, 
    image.targetWidth, 
    image.targetHeight,
    image.resizeMode
  ]);
  
  return <img src={previewUrl} alt={image.name} />;
};
```

## 最佳实践

### 1. 错误处理

```typescript
const safeProcessImage = async (imageUrl: string, config: ImageProcessingConfig) => {
  try {
    return await processImagePipeline(imageUrl, null, config);
  } catch (error) {
    console.error('图片处理失败:', error);
    
    // 提供回退方案
    return {
      dataUrl: imageUrl, // 使用原始图片
      width: 0,
      height: 0,
      size: 0,
      format: 'png' as const
    };
  }
};
```

### 2. 性能优化

```typescript
// 批量处理时使用并发
const processBatchImages = async (images: ImageState[], config: ImageProcessingConfig) => {
  const promises = images.map(async (image) => {
    try {
      return await processImagePipeline(image.previewUrl, image.processedUrl, config);
    } catch (error) {
      console.error(`图片 ${image.name} 处理失败:`, error);
      return null;
    }
  });
  
  const results = await Promise.all(promises);
  return results.filter(result => result !== null);
};
```

### 3. 内存管理

```typescript
// 及时清理资源
const processAndCleanup = async (imageUrl: string, config: ImageProcessingConfig) => {
  const result = await processImagePipeline(imageUrl, null, config);
  
  // 使用完毕后清理
  setTimeout(() => {
    if (result.dataUrl.startsWith('blob:')) {
      URL.revokeObjectURL(result.dataUrl);
    }
  }, 5000); // 5秒后清理
  
  return result;
};
```

## 调试技巧

### 1. 启用详细日志

```typescript
// 在开发环境中启用详细日志
const DEBUG = process.env.NODE_ENV === 'development';

const processWithLogging = async (imageUrl: string, config: ImageProcessingConfig) => {
  if (DEBUG) {
    console.log('开始处理图片:', { imageUrl, config });
    const startTime = performance.now();
    
    const result = await processImagePipeline(imageUrl, null, config);
    
    const endTime = performance.now();
    console.log('处理完成:', {
      耗时: `${endTime - startTime}ms`,
      结果: result
    });
    
    return result;
  }
  
  return processImagePipeline(imageUrl, null, config);
};
```

### 2. 监控内存使用

```typescript
const monitorMemory = () => {
  if (performance.memory) {
    console.log('内存使用情况:', {
      已使用: `${Math.round(performance.memory.usedJSHeapSize / 1024 / 1024)}MB`,
      总计: `${Math.round(performance.memory.totalJSHeapSize / 1024 / 1024)}MB`,
      限制: `${Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)}MB`
    });
  }
};
```

## 常见问题解决

### Q: 预览和下载结果不一致？
A: 确保使用统一的处理管道，检查 `compositePreviewUrl` 是否正确更新。

### Q: 大批量处理时页面卡顿？
A: 使用 `Promise.all` 并发处理，或者分批处理大量图片。

### Q: 内存占用过高？
A: 及时清理生成的 blob URL，限制同时处理的图片数量。

### Q: 某些格式不支持？
A: 检查 `SupportedFormat` 类型定义，确保使用支持的格式。

## 下一步

- 查看 [技术文档](./统一图片处理管道技术文档.md) 了解详细实现
- 查看 [API参考](./统一图片处理管道API参考.md) 了解完整接口
- 在实际项目中集成并测试各种场景

通过这个快速开始指南，您应该能够快速集成统一图片处理管道到您的项目中。如有问题，请参考详细的技术文档或联系开发团队。
