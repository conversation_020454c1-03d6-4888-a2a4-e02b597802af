// 支持的图片格式常量
export const SUPPORTED_IMAGE_TYPES = [
  'image/jpeg',
  'image/jpg',
  'image/png',
  'image/webp',
];

export const SUPPORTED_FILE_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.webp'];

// 文件大小限制：10MB
export const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB in bytes

// 示例图片列表
export const SAMPLE_IMAGES = [
  {
    id: 'sample_dog',
    name: '人像',
    url: '/apps/images/mock/01_color.png',
    processedUrl: '/apps/images/mock/01_alpha.png',
  },
  {
    id: 'sample_cat',
    name: '宠物',
    url: '/apps/images/mock/02_color.png',
    processedUrl: '/apps/images/mock/02_alpha.png',
  },
  {
    id: 'sample_person',
    name: '物品',
    url: '/apps/images/mock/03_color.png',
    processedUrl: '/apps/images/mock/03_alpha.png',
  },
  {
    id: 'sample_object',
    name: '产品',
    url: '/apps/images/mock/04_color.png',
    processedUrl: '/apps/images/mock/04_alpha.png',
  },
];

// UI相关常量
export const MAX_SINGLE_IMAGES_LIMIT = 10; // 单图上传限制
export const MAX_BATCH_IMAGES_LIMIT = 50; // 批量上传限制
export const MAX_ZOOM_SCALE = 3;
export const ZOOM_STEP = 50;
export const MIN_DISPLAY_WIDTH = 400;
export const MIN_DISPLAY_HEIGHT = 530;
export const MAX_CANVAS_WIDTH = 828;
export const ERASE_HISTORY_LIMIT = 50;
