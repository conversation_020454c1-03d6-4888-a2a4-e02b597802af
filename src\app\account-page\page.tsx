'use client';

import { DesktopPage } from '@/components/desktop-page';
import { MobileAccount } from '@/components/mobile-account';
import { useDevice } from '@/context/DeviceContext';

/**
 * 去除背景页面
 * 根据设备类型动态渲染适配的页面组件：
 * - 移动端：渲染 MobilePage 组件，提供触摸友好的界面
 * - 桌面端：渲染 DesktopPage 组件，提供完整的桌面体验
 */
export default function RemoveBackground() {
  const { isMobile } = useDevice();

  // 根据设备类型返回对应的页面组件
  if (isMobile) {
    return <MobileAccount />;
  }

  return <DesktopPage />;
}
