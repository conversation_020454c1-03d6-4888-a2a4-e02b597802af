'use client';

import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useLayoutEffect,
  useRef,
  useState,
} from 'react';

/**
 * MobileCanvasImageEditor 组件的 Props。
 */
interface MobileCanvasImageEditorProps {
  // 原始的、已加载的 HTMLImageElement 对象。
  originalImgObject: HTMLImageElement | null;
  // 由 API 返回的处理后图像的 URL。
  processedUrl?: string | null;
  // 用于本地测试或模拟的直接图像路径。
  mockImagePaths?: {
    colorUrl: string;
    alphaUrl: string;
  };
  // 画布的背景颜色。
  backgroundColor?: string;
  // 画布的背景图片 URL。
  backgroundImageUrl?: string;
  // 是否激活对比模式（显示原图）。
  isCompareModeActive?: boolean;
  // 图像的缩放级别。
  scale?: number;
  // 当需要更改缩放级别时请求父组件更新的回调函数。
  onScaleChangeRequest?: (newScale: number) => void;
  // 是否启用背景模糊效果。
  isBlurEnabled?: boolean;
  // 背景模糊的程度（像素值）。
  blurAmount?: number;
  // 容器宽度，用于计算。
  containerWidth?: number;
  // 容器高度，用于计算。
  containerHeight?: number;
  // 橡皮擦相关属性
  isEraseMode?: boolean;
  eraseBrushSize?: number;
  // 恢复模式（用于恢复被橡皮擦擦除的部分）
  isRestoreMode?: boolean;
  // 橡皮擦操作完成回调（包含历史数据和当前画布数据）
  onEraseOperationComplete?: (data: {
    historyData: ImageData | null;
    currentCanvasData: ImageData | null;
  }) => void;
}

/**
 * 通过 ref 暴露给父组件的句柄和方法。
 */
export interface MobileCanvasImageEditorHandles {
  // 重置视图（缩放和位置）。
  resetView: () => void;
  // 获取可供下载的图像数据（base64 格式的 PNG）。
  getDownloadableImageData: () => Promise<string | null>;
  // 清空橡皮擦图层
  clearEraseLayer: () => void;
  // 恢复橡皮擦画布到指定状态
  restoreEraseCanvasData: (canvasData: ImageData) => void;
  // 获取当前橡皮擦数据
  getCurrentEraseData: () => ImageData | null;
}

/**
 * 包含从 ZIP 解压出的颜色和 Alpha 图层图像。
 */
interface ProcessedImageData {
  colorImage: HTMLImageElement;
  alphaImage: HTMLImageElement;
}

export const MobileCanvasImageEditor = forwardRef<
  MobileCanvasImageEditorHandles,
  MobileCanvasImageEditorProps
>(
  (
    {
      originalImgObject,
      processedUrl,
      mockImagePaths,
      backgroundColor,
      backgroundImageUrl,
      isCompareModeActive = false,
      scale = 1,
      onScaleChangeRequest,
      isBlurEnabled = false,
      blurAmount = 0,
      containerWidth = 0,
      containerHeight = 0,
      isEraseMode = false,
      eraseBrushSize = 10,
      isRestoreMode = false,
      onEraseOperationComplete,
    },
    ref
  ) => {
    // Canvas 引用
    const backgroundCanvasRef = useRef<HTMLCanvasElement>(null); // 背景层 (颜色、棋盘格、背景图)
    const baseLayerCanvasRef = useRef<HTMLCanvasElement>(null); // 主图像层 (处理后的图)
    const compareWipeCanvasRef = useRef<HTMLCanvasElement>(null); // 对比模式下的原图擦除层
    const eraseCanvasRef = useRef<HTMLCanvasElement>(null); // 橡皮擦层
    const containerRef = useRef<HTMLDivElement>(null); // 整个组件的容器引用，用于获取视口尺寸

    // 组件状态
    const [processedData, setProcessedData] =
      useState<ProcessedImageData | null>(null); // 解压后的图像数据
    const [processedImageObj, setProcessedImageObj] =
      useState<HTMLImageElement | null>(null); // 处理后的图片对象
    const [compareProgress, setCompareProgress] = useState(0); // 对比模式的擦除动画进度 (0 到 1)
    const animationFrameRef = useRef<number | null>(null); // 对比动画的 requestAnimationFrame 引用
    const [backgroundImageObj, setBackgroundImageObj] =
      useState<HTMLImageElement | null>(null); // 背景图片的 Image 对象

    // 平移和缩放状态
    const isMouseDownRef = useRef(false); // 鼠标是否按下
    const [offsetX, setOffsetX] = useState(0); // 图像的 X 轴偏移
    const [offsetY, setOffsetY] = useState(0); // 图像的 Y 轴偏移
    const [isDragging, setIsDragging] = useState(false); // 是否正在拖动
    const lastMousePosRef = useRef({ x: 0, y: 0 }); // 上一次鼠标位置，用于计算拖动增量

    // 手势操作的初始状态
    const pinchStartDataRef = useRef<{
      initialDistance: number; // 初始双指距离
      initialScale: number; // 初始缩放比例
      pinchCenterX: number; // 双指中心点的 X 坐标
      pinchCenterY: number; // 双指中心点的 Y 坐标
      startOffsetX: number; // 开始平移时的 X 偏移
      startOffsetY: number; // 开始平移时的 Y 偏移
    } | null>(null);

    // 橡皮擦相关状态
    const [isErasing, setIsErasing] = useState(false); // 是否正在擦除
    const [eraseHistorySnapshot, setEraseHistorySnapshot] =
      useState<ImageData | null>(null); // 橡皮擦操作开始时的画布快照
    const lastErasePointRef = useRef<{ x: number; y: number } | null>(null); // 上一个擦除点，用于连续绘制

    const cursorElementRef = useRef<HTMLDivElement | null>(null); // 光圈元素引用，用于性能优化

    // 用于精确缩放计算的引用
    const prevScaleRef = useRef(scale); // 上一次的缩放值
    const committedOffsetXRef = useRef(0); // 已提交的 X 轴偏移（拖动结束后）
    const committedOffsetYRef = useRef(0); // 已提交的 Y 轴偏移（拖动结束后）
    const prevOriginalImgObjectRef = useRef<HTMLImageElement | null>(null); // 上一个图像对象，用于检测图像变化

    const { loadDirectImages } = useImageProcessing();

    // 动态容器样式，用于确保容器的宽高比与图片一致
    const [dynamicContainerStyle, setDynamicContainerStyle] =
      useState<React.CSSProperties>({
        visibility: 'hidden',
        width: '0px',
        height: '0px',
      });

    // 根据原始图片对象和传入的容器尺寸，设置容器的样式
    useLayoutEffect(() => {
      if (
        originalImgObject &&
        originalImgObject.naturalWidth > 0 &&
        originalImgObject.naturalHeight > 0 &&
        containerWidth > 0 &&
        containerHeight > 0
      ) {
        setDynamicContainerStyle({
          display: 'block',
          width: `${containerWidth}px`,
          height: `${containerHeight}px`,
          maxWidth: '100%',
          maxHeight: 'calc(100vh - 488px)',
          overflow: 'hidden',
        });
      } else {
        // 如果没有有效的图片，则隐藏容器
        setDynamicContainerStyle({
          visibility: 'hidden',
          width: '0px',
          height: '0px',
        });
      }
    }, [originalImgObject, containerWidth, containerHeight]);

    /**
     * 重置视图，使图像居中并恢复到初始缩放状态。
     */
    const resetView = useCallback(() => {
      if (
        !originalImgObject ||
        !containerRef.current ||
        !containerWidth ||
        !containerHeight
      )
        return;

      const vpWidth = containerWidth;
      const vpHeight = containerHeight;

      const imgNaturalWidth = originalImgObject.naturalWidth;
      const imgNaturalHeight = originalImgObject.naturalHeight;

      const scaledWidth = imgNaturalWidth * scale;
      const scaledHeight = imgNaturalHeight * scale;
      const finalOffsetX = (vpWidth - scaledWidth) / 2;
      const finalOffsetY = (vpHeight - scaledHeight) / 2;

      setOffsetX(finalOffsetX);
      setOffsetY(finalOffsetY);
      committedOffsetXRef.current = finalOffsetX;
      committedOffsetYRef.current = finalOffsetY;
      prevScaleRef.current = scale;
    }, [originalImgObject, scale, containerWidth, containerHeight]);

    /**
     * 重置橡皮擦图层
     */
    const resetEraseLayer = useCallback(() => {
      const canvas = eraseCanvasRef.current;
      if (!canvas || !originalImgObject) return;

      const scaledWidth = originalImgObject.naturalWidth * scale;
      const scaledHeight = originalImgObject.naturalHeight * scale;
      canvas.width = scaledWidth;
      canvas.height = scaledHeight;

      const ctx = canvas.getContext('2d');
      if (!ctx) return;

      // 清空画布并设置为完全透明
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // 初始化画布为透明状态
    }, [originalImgObject, scale]);

    /**
     * 在橡皮擦图层上绘制效果（橡皮擦或恢复）
     * 坐标基于当前缩放后的画布尺寸
     */
    const drawEraseStroke = useCallback(
      (
        startX: number,
        startY: number,
        endX: number,
        endY: number,
        brushSize: number
      ) => {
        const canvas = eraseCanvasRef.current;
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        if (!ctx) return;

        ctx.save();
        ctx.lineCap = 'round';
        ctx.lineJoin = 'round';
        ctx.lineWidth = brushSize * scale; // 画笔大小根据缩放调整

        if (isRestoreMode) {
          // 恢复模式：擦除橡皮擦图层上的内容（恢复下方图像）
          ctx.globalCompositeOperation = 'destination-out';
          ctx.strokeStyle = '#transparent'; // 这里颜色不重要，只是擦除
          ctx.beginPath();
          ctx.moveTo(startX, startY);
          ctx.lineTo(endX, endY);
          ctx.stroke();
        } else {
          // 橡皮擦模式：在橡皮擦图层上绘制棋盘格图案，模拟透明效果
          ctx.globalCompositeOperation = 'source-over';
          // 创建跟随缩放的棋盘格图案，与底层背景视觉效果一致
          const BASE_TILE_SIZE = 10; // 基础棋盘格大小，与底层背景一致
          const scaledTileSize = BASE_TILE_SIZE * scale; // 根据缩放调整大小
          const patternCanvas = document.createElement('canvas');
          patternCanvas.width = scaledTileSize * 2;
          patternCanvas.height = scaledTileSize * 2;
          const patternCtx = patternCanvas.getContext('2d')!;

          patternCtx.fillStyle = '#fafafa';
          patternCtx.fillRect(0, 0, scaledTileSize * 2, scaledTileSize * 2);
          patternCtx.fillStyle = '#f0f0f0';
          patternCtx.fillRect(0, 0, scaledTileSize, scaledTileSize);
          patternCtx.fillRect(
            scaledTileSize,
            scaledTileSize,
            scaledTileSize,
            scaledTileSize
          );

          const pattern = ctx.createPattern(patternCanvas, 'repeat');
          if (pattern) {
            ctx.strokeStyle = pattern;
            ctx.beginPath();
            ctx.moveTo(startX, startY);
            ctx.lineTo(endX, endY);
            ctx.stroke();
          }
        }

        ctx.restore();
      },
      [isRestoreMode, scale]
    );

    /**
     * 开始橡皮擦操作
     */
    const handleEraseStart = useCallback(
      (clientX: number, clientY: number) => {
        if (
          !(isEraseMode || isRestoreMode) ||
          !originalImgObject ||
          !containerRef.current ||
          !eraseCanvasRef.current
        )
          return;

        const viewport = containerRef.current;
        const rect = viewport.getBoundingClientRect();

        // 将窗口坐标转换为相对于视口的坐标
        const viewportX = clientX - rect.left;
        const viewportY = clientY - rect.top;

        // 移动端坐标转换逻辑：
        // 图像容器通过CSS居中：top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2
        // 然后再应用offsetX和offsetY偏移（用户拖拽产生的偏移）
        const containerCenterX = containerWidth / 2;
        const containerCenterY = containerHeight / 2;

        // 图像容器中心的实际位置（视口中心 + 用户偏移）
        const imageCenterX = containerCenterX + offsetX;
        const imageCenterY = containerCenterY + offsetY;

        // 计算点击位置相对于图像中心的偏移
        const relativeToImageCenterX = viewportX - imageCenterX;
        const relativeToImageCenterY = viewportY - imageCenterY;

        // 转换为图像坐标系（相对于图像左上角的像素坐标）
        // 图像中心对应图像坐标系的 (naturalWidth/2, naturalHeight/2)
        const imageX =
          relativeToImageCenterX / scale + originalImgObject.naturalWidth / 2;
        const imageY =
          relativeToImageCenterY / scale + originalImgObject.naturalHeight / 2;

        // 确保坐标在图像范围内
        if (
          imageX < 0 ||
          imageY < 0 ||
          imageX > originalImgObject.naturalWidth ||
          imageY > originalImgObject.naturalHeight
        ) {
          return;
        }

        // 保存橡皮擦操作开始前的画布状态（在任何绘制操作之前）
        const canvas = eraseCanvasRef.current;
        const ctx = canvas.getContext('2d');
        let historySnapshot: ImageData | null = null;
        if (ctx) {
          historySnapshot = ctx.getImageData(0, 0, canvas.width, canvas.height);
        }

        setIsErasing(true);
        // 转换为画布坐标系（缩放后的坐标）
        const scaledX = imageX * scale;
        const scaledY = imageY * scale;
        lastErasePointRef.current = { x: scaledX, y: scaledY };

        // 绘制初始点
        drawEraseStroke(scaledX, scaledY, scaledX, scaledY, eraseBrushSize);

        // 在绘制完成后再设置历史快照，确保快照是绘制前的状态
        setEraseHistorySnapshot(historySnapshot);
      },
      [
        isEraseMode,
        isRestoreMode,
        originalImgObject,
        offsetX,
        offsetY,
        scale,
        eraseBrushSize,
        drawEraseStroke,
        containerWidth,
        containerHeight,
      ]
    );

    /**
     * 结束橡皮擦操作
     */
    const handleEraseEnd = useCallback(() => {
      // 防止重复调用：如果不处于擦除状态，直接返回
      if (!isErasing) return;

      // 获取当前画布状态并转换为原始尺寸
      let currentCanvasData: ImageData | null = null;
      if (eraseCanvasRef.current && originalImgObject) {
        const canvas = eraseCanvasRef.current;
        const ctx = canvas.getContext('2d');
        if (ctx) {
          const scaledImageData = ctx.getImageData(
            0,
            0,
            canvas.width,
            canvas.height
          );

          // 将缩放后的橡皮擦数据转换回原始尺寸
          const originalSizeCanvas = document.createElement('canvas');
          originalSizeCanvas.width = originalImgObject.naturalWidth;
          originalSizeCanvas.height = originalImgObject.naturalHeight;
          const originalSizeCtx = originalSizeCanvas.getContext('2d');

          if (originalSizeCtx) {
            const tempCanvas = document.createElement('canvas');
            tempCanvas.width = scaledImageData.width;
            tempCanvas.height = scaledImageData.height;
            const tempCtx = tempCanvas.getContext('2d');

            if (tempCtx) {
              tempCtx.putImageData(scaledImageData, 0, 0);
              // 缩放回原始尺寸
              originalSizeCtx.drawImage(
                tempCanvas,
                0,
                0,
                tempCanvas.width,
                tempCanvas.height,
                0,
                0,
                originalSizeCanvas.width,
                originalSizeCanvas.height
              );

              currentCanvasData = originalSizeCtx.getImageData(
                0,
                0,
                originalSizeCanvas.width,
                originalSizeCanvas.height
              );

              // 同时更新 ref
              savedEraseDataRef.current = currentCanvasData;
            }
          }
        }
      }

      // 一次性调用回调，传递历史数据和当前画布数据（原始尺寸）
      if (onEraseOperationComplete) {
        onEraseOperationComplete({
          historyData: eraseHistorySnapshot,
          currentCanvasData,
        });
      }

      setIsErasing(false);
      setEraseHistorySnapshot(null);
      lastErasePointRef.current = null;
      // 隐藏光圈
      if (cursorElementRef.current) {
        cursorElementRef.current.style.display = 'none';
      }
    }, [
      isErasing,
      eraseHistorySnapshot,
      onEraseOperationComplete,
      originalImgObject,
    ]);

    /**
     * 橡皮擦移动
     */
    const handleEraseMove = useCallback(
      (clientX: number, clientY: number) => {
        if (!containerRef.current || !originalImgObject) return;

        const viewport = containerRef.current;
        const rect = viewport.getBoundingClientRect();

        // 将窗口坐标转换为相对于视口的坐标
        const viewportX = clientX - rect.left;
        const viewportY = clientY - rect.top;

        // 更新鼠标位置用于预览（优化性能，直接操作DOM）
        if (isEraseMode || isRestoreMode) {
          if (cursorElementRef.current) {
            const halfSize = eraseBrushSize / 2;
            cursorElementRef.current.style.transform = `translate3d(${viewportX - halfSize}px, ${viewportY - halfSize}px, 0)`;
            cursorElementRef.current.style.display = 'block';
            // 根据模式改变光圈颜色
            cursorElementRef.current.style.backgroundColor = isRestoreMode
              ? '#00ff00'
              : '#ff0000';
          }
        } else {
          // 隐藏光圈
          if (cursorElementRef.current) {
            cursorElementRef.current.style.display = 'none';
          }
        }

        if (!isErasing || !lastErasePointRef.current) return;

        // 移动端坐标转换逻辑：
        // 图像容器通过CSS居中：top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2
        // 然后再应用offsetX和offsetY偏移（用户拖拽产生的偏移）
        const containerCenterX = containerWidth / 2;
        const containerCenterY = containerHeight / 2;

        // 图像容器中心的实际位置（视口中心 + 用户偏移）
        const imageCenterX = containerCenterX + offsetX;
        const imageCenterY = containerCenterY + offsetY;

        // 计算点击位置相对于图像中心的偏移
        const relativeToImageCenterX = viewportX - imageCenterX;
        const relativeToImageCenterY = viewportY - imageCenterY;

        // 转换为图像坐标系（相对于图像左上角的像素坐标）
        // 图像中心对应图像坐标系的 (naturalWidth/2, naturalHeight/2)
        const imageX =
          relativeToImageCenterX / scale + originalImgObject.naturalWidth / 2;
        const imageY =
          relativeToImageCenterY / scale + originalImgObject.naturalHeight / 2;

        // 确保坐标在图像范围内
        if (
          imageX < 0 ||
          imageY < 0 ||
          imageX > originalImgObject.naturalWidth ||
          imageY > originalImgObject.naturalHeight
        ) {
          return;
        }

        // 转换为画布坐标系（缩放后的坐标）
        const scaledX = imageX * scale;
        const scaledY = imageY * scale;

        // 绘制从上一个点到当前点的线条
        drawEraseStroke(
          lastErasePointRef.current.x,
          lastErasePointRef.current.y,
          scaledX,
          scaledY,
          eraseBrushSize
        );

        lastErasePointRef.current = { x: scaledX, y: scaledY };
      },
      [
        isEraseMode,
        isRestoreMode,
        isErasing,
        originalImgObject,
        offsetX,
        offsetY,
        scale,
        eraseBrushSize,
        drawEraseStroke,
        containerWidth,
        containerHeight,
      ]
    );

    /**
     * 清空橡皮擦图层
     */
    const clearEraseLayer = useCallback(() => {
      const canvas = eraseCanvasRef.current;
      if (!canvas) return;

      const ctx = canvas.getContext('2d');
      if (!ctx) return;

      // 清空画布
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      // 同时清空 ref 中的数据
      savedEraseDataRef.current = null;
    }, []);

    /**
     * 恢复橡皮擦画布到指定状态
     */
    const restoreEraseCanvasData = useCallback(
      (canvasData: ImageData) => {
        const canvas = eraseCanvasRef.current;
        if (!canvas || !originalImgObject) return;

        const ctx = canvas.getContext('2d');
        if (!ctx) return;

        // 更新 ref 中的数据
        savedEraseDataRef.current = canvasData;

        // 清空画布
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // 检查数据尺寸是否与当前画布尺寸匹配
        if (
          canvasData.width === canvas.width &&
          canvasData.height === canvas.height
        ) {
          // 尺寸匹配，直接恢复
          ctx.putImageData(canvasData, 0, 0);
          console.log('直接恢复橡皮擦数据');
        } else {
          // 尺寸不匹配，需要缩放
          // 假设 canvasData 是原始尺寸的数据，需要缩放到当前尺寸
          const tempCanvas = document.createElement('canvas');
          tempCanvas.width = canvasData.width;
          tempCanvas.height = canvasData.height;
          const tempCtx = tempCanvas.getContext('2d');

          if (tempCtx) {
            tempCtx.putImageData(canvasData, 0, 0);
            // 缩放到当前画布尺寸
            ctx.drawImage(
              tempCanvas,
              0,
              0,
              tempCanvas.width,
              tempCanvas.height,
              0,
              0,
              canvas.width,
              canvas.height
            );
            console.log('缩放恢复橡皮擦数据');
          }
        }
      },
      [originalImgObject, scale]
    );

    // 生成并返回当前画布视图的可下载图像数据。
    const getDownloadableImageData = useCallback(async (): Promise<
      string | null
    > => {
      if (
        !originalImgObject ||
        !containerRef.current ||
        !baseLayerCanvasRef.current
      ) {
        console.error(
          '下载失败：不满足前提条件（原始图片、容器或基础图层不存在）。'
        );
        return null;
      }

      const viewport = containerRef.current;
      const downloadCanvas = document.createElement('canvas');
      downloadCanvas.width = viewport.clientWidth;
      downloadCanvas.height = viewport.clientHeight;
      const dlCtx = downloadCanvas.getContext('2d');

      if (!dlCtx) {
        console.error('无法为下载画布获取 2D 上下文。');
        return null;
      }

      // 1. 绘制背景层
      dlCtx.clearRect(0, 0, downloadCanvas.width, downloadCanvas.height);

      if (backgroundImageObj) {
        // 绘制背景图片，确保它以 "cover" 的方式填充整个画布
        const imgAspect =
          backgroundImageObj.naturalWidth / backgroundImageObj.naturalHeight;
        const canvasAspect = downloadCanvas.width / downloadCanvas.height;
        let sx, sy, sWidth, sHeight;

        if (imgAspect > canvasAspect) {
          sHeight = backgroundImageObj.naturalHeight;
          sWidth = sHeight * canvasAspect;
          sx = (backgroundImageObj.naturalWidth - sWidth) / 2;
          sy = 0;
        } else {
          sWidth = backgroundImageObj.naturalWidth;
          sHeight = sWidth / canvasAspect;
          sx = 0;
          sy = (backgroundImageObj.naturalHeight - sHeight) / 2;
        }
        dlCtx.drawImage(
          backgroundImageObj,
          sx,
          sy,
          sWidth,
          sHeight,
          0,
          0,
          downloadCanvas.width,
          downloadCanvas.height
        );
      } else if (backgroundColor && backgroundColor !== 'transparent') {
        dlCtx.fillStyle = backgroundColor;
        dlCtx.fillRect(0, 0, downloadCanvas.width, downloadCanvas.height);
      }

      // 2. 绘制主图像层（已缩放和偏移）
      dlCtx.drawImage(
        baseLayerCanvasRef.current,
        offsetX,
        offsetY,
        baseLayerCanvasRef.current.width,
        baseLayerCanvasRef.current.height
      );

      // 3. 如果在对比模式下，绘制对比擦除层
      if (
        isCompareModeActive &&
        compareProgress > 0 &&
        compareWipeCanvasRef.current
      ) {
        dlCtx.drawImage(
          compareWipeCanvasRef.current,
          offsetX,
          offsetY,
          compareWipeCanvasRef.current.width,
          compareWipeCanvasRef.current.height
        );
      }

      // 4. 绘制橡皮擦层
      if (eraseCanvasRef.current) {
        dlCtx.drawImage(
          eraseCanvasRef.current,
          offsetX,
          offsetY,
          eraseCanvasRef.current.width,
          eraseCanvasRef.current.height
        );
      }

      return downloadCanvas.toDataURL('image/png');
    }, [
      originalImgObject,
      containerRef,
      baseLayerCanvasRef,
      compareWipeCanvasRef,
      eraseCanvasRef,
      offsetX,
      offsetY,
      isCompareModeActive,
      compareProgress,
      backgroundColor,
      backgroundImageObj,
    ]);

    /**
     * 获取当前橡皮擦数据
     */
    const getCurrentEraseData = useCallback(() => {
      return savedEraseDataRef.current;
    }, []);

    // 存储初始缩放比例
    const initialScaleRef = useRef<number>(scale);

    useImperativeHandle(ref, () => ({
      resetView,
      getDownloadableImageData,
      clearEraseLayer, // 暴露清空橡皮擦图层功能给父组件
      restoreEraseCanvasData, // 暴露恢复橡皮擦画布状态功能给父组件
      getCurrentEraseData, // 暴露获取当前橡皮擦数据功能给父组件
    }));

    // 当图片对象或缩放比例变化时触发的 Effect
    useEffect(() => {
      if (!originalImgObject) {
        setOffsetX(0);
        setOffsetY(0);
        committedOffsetXRef.current = 0;
        committedOffsetYRef.current = 0;
        prevScaleRef.current = 1;
        onScaleChangeRequest?.(1);
        return;
      }
    }, [originalImgObject, onScaleChangeRequest, scale]);

    // 处理以视图中心为基点的缩放效果
    useLayoutEffect(() => {
      if (!originalImgObject || !containerRef.current) {
        return;
      }

      // 如果是全新的图片，则调用 resetView 来居中，不应用此处的缩放逻辑
      if (prevOriginalImgObjectRef.current !== originalImgObject) {
        resetView();
        prevOriginalImgObjectRef.current = originalImgObject;
        prevScaleRef.current = scale;
        return;
      }

      prevScaleRef.current = scale;
      // 初始化时执行此段代码，缩放过程不执行
      if (scale == initialScaleRef.current) {
        const lastCommittedScale = prevScaleRef.current;
        const lastCommittedOffsetX = committedOffsetXRef.current;
        const lastCommittedOffsetY = committedOffsetYRef.current;

        const viewportCenterX = containerWidth / 2;
        const viewportCenterY = containerHeight / 2;

        // 计算新的偏移量以保持缩放中心不变
        const scaleRatio =
          lastCommittedScale === 0 ? 1 : scale / lastCommittedScale;

        const newOffsetX =
          viewportCenterX -
          (viewportCenterX - lastCommittedOffsetX) * scaleRatio;
        const newOffsetY =
          viewportCenterY -
          (viewportCenterY - lastCommittedOffsetY) * scaleRatio;

        setOffsetX(newOffsetX);
        setOffsetY(newOffsetY);

        // 更新下一次渲染所需的前置状态
        committedOffsetXRef.current = newOffsetX;
        committedOffsetYRef.current = newOffsetY;
      }
    }, [
      scale,
      originalImgObject,
      resetView,
      containerWidth,
      containerHeight,
      initialScaleRef,
    ]);

    // 触摸开始
    const handleTouchStart = (event: TouchEvent) => {
      if (event.touches.length >= 2) {
        // 如果是双指

        // 双指坐标
        const touch1 = event.touches[0];
        const touch2 = event.touches[1];

        // 包裹容器的边界
        const rect = containerRef.current?.getBoundingClientRect();

        // 设置缩放中心点
        const touchCenterX =
          (touch1.clientX + touch2.clientX) / 2 - (rect?.left || 0);
        const touchCenterY =
          (touch1.clientY + touch2.clientY) / 2 - (rect?.top || 0);

        // 计算触摸中心点相对于图片中心的偏移
        const containerCenterX = containerWidth / 2;
        const containerCenterY = containerHeight / 2;

        // 计算并记录所有初始状态
        pinchStartDataRef.current = {
          initialDistance: Math.hypot(
            touch1.clientX - touch2.clientX,
            touch1.clientY - touch2.clientY
          ),
          initialScale: prevScaleRef.current,
          pinchCenterX: touchCenterX - containerCenterX,
          pinchCenterY: touchCenterY - containerCenterY,
          // 存储开始缩放时的偏移量
          startOffsetX: offsetX,
          startOffsetY: offsetY,
        };
      } else {
        // 如果是单指
        if (isEraseMode || isRestoreMode) {
          handleEraseStart(event.touches[0].clientX, event.touches[0].clientY);
        } else {
          handlePanStart(event.touches[0].clientX, event.touches[0].clientY);
        }
      }
    };

    // 触摸移动中
    const handleTouchMove = (event: TouchEvent) => {
      // 确保有两个或更多触摸点
      if (event.touches.length >= 2) {
        // // 获取第一个和第二个触摸点
        const touch1 = event.touches[0];
        const touch2 = event.touches[1];

        if (!pinchStartDataRef.current) return;

        const {
          initialDistance,
          initialScale,
          pinchCenterX,
          pinchCenterY,
          startOffsetX,
          startOffsetY,
        } = pinchStartDataRef.current;

        // 计算缩放比例并传递出去
        const currentDistance = Math.hypot(
          touch1.clientX - touch2.clientX,
          touch1.clientY - touch2.clientY
        );

        // 应用缩放比例（相对于当前缩放值）
        const newScale = (currentDistance / initialDistance) * initialScale;

        // 限制缩放范围，防止过度缩放或缩小
        // 最小为初始比例，最大为3倍
        const limitedScale = Math.max(
          initialScaleRef.current,
          Math.min(newScale, 3)
        );

        // 计算缩放比例  新倍数 / 初始倍数
        const scaleRatio = limitedScale / initialScale;

        // 3. 计算新的偏移量，保持中心点稳定
        const newOffsetX = startOffsetX + pinchCenterX * (1 - scaleRatio);
        const newOffsetY = startOffsetY + pinchCenterY * (1 - scaleRatio);

        // 4. 更新状态
        updateTransform(newOffsetX, newOffsetY, limitedScale);
      } else {
        if (isEraseMode || isRestoreMode) {
          handleEraseMove(event.touches[0].clientX, event.touches[0].clientY);
        } else {
          handlePanMove(event.touches[0].clientX, event.touches[0].clientY);
        }
      }
    };

    // 添加一个防抖函数
    const debouncedScaleChange = (scale: number) => {
      onScaleChangeRequest?.(scale);
    };

    // 根据浏览器刷新率更新状态
    const updateTransform = useCallback(
      (newOffsetX: number, newOffsetY: number, newScale: number) => {
        requestAnimationFrame(() => {
          setOffsetX(newOffsetX);
          setOffsetY(newOffsetY);
          prevScaleRef.current = newScale;
          debouncedScaleChange(newScale);
        });
      },
      [debouncedScaleChange]
    );

    // 触摸结束
    const handleTouchEnd = () => {
      if (pinchStartDataRef.current) {
        // 手势结束，提交当前的偏移量
        committedOffsetXRef.current = offsetX;
        committedOffsetYRef.current = offsetY;
        // 清空手势初始状态
        pinchStartDataRef.current = null;
        prevScaleRef.current = scale;
      } else {
        if (isEraseMode || isRestoreMode) {
          handleEraseEnd();
        } else {
          handlePanEnd();
        }
      }
    };

    // 开始平移
    const handlePanStart = (clientX: number, clientY: number) => {
      if (!originalImgObject || !containerRef.current) return;

      const viewport = containerRef.current;
      const scaledWidth = originalImgObject.naturalWidth * scale;
      const scaledHeight = originalImgObject.naturalHeight * scale;

      const isPannable =
        scaledWidth > viewport.clientWidth ||
        scaledHeight > viewport.clientHeight;

      if (!isPannable) return;

      // 修复：设置拖动状态并记录起始坐标（使用窗口坐标系）
      lastMousePosRef.current = { x: clientX, y: clientY };
      isMouseDownRef.current = true;
      setIsDragging(true);
    };

    // 平移中
    const handlePanMove = (clientX: number, clientY: number) => {
      // 修复：不再需要此处的重复检查，并统一使用窗口坐标系
      const deltaX = clientX - lastMousePosRef.current.x;
      const deltaY = clientY - lastMousePosRef.current.y;

      const newOffsetX = committedOffsetXRef.current + deltaX;
      const newOffsetY = committedOffsetYRef.current + deltaY;

      // --- 边界限制逻辑保持不变 ---
      if (!originalImgObject || !containerRef.current) return;
      const viewport = containerRef.current;
      const scaledWidth = originalImgObject.naturalWidth * scale; //  缩放后的宽度
      const scaledHeight = originalImgObject.naturalHeight * scale; //  缩放后的高度
      const viewportWidth = viewport.clientWidth; // 视口宽度
      const viewportHeight = viewport.clientHeight; // 视口高度

      // 允许图片尽量多地拖出视口，但边缘保留minWidthBorder的空间
      const minWidthBorder = 20; // 预留的空间

      // 计算水平偏移量
      let finalX = newOffsetX;
      if (scaledWidth > viewportWidth) {
        // 当图片比容器大时
        const maxDragLeft =
          -(scaledWidth / 2) - viewportWidth / 2 + minWidthBorder; // 左边可以拖到只剩10px;
        const maxDragRight =
          viewportWidth / 2 + scaledWidth / 2 - minWidthBorder; // 右边可以拖到只剩10px
        if (finalX <= 0) {
          finalX = Math.max(finalX, maxDragLeft); // 左移的边界
        } else {
          finalX = Math.min(finalX, maxDragRight); // 右移的边界
        }
      } else {
        finalX = (viewportWidth - scaledWidth) / 2; // 居中
      }

      // 计算垂直偏移量
      let finalY = newOffsetY;
      if (scaledHeight > viewportHeight) {
        // 当图片比容器大时
        const maxDragTop =
          -(viewportHeight / 2) - scaledHeight / 2 + minWidthBorder; // 上边可以拖到只剩10px;
        const maxDragBottom =
          viewportHeight / 2 + scaledHeight / 2 - minWidthBorder; // 下边可以拖到只剩10px
        if (finalY <= 0) {
          finalY = Math.max(finalY, maxDragTop); // 上移的边界
        } else {
          finalY = Math.min(finalY, maxDragBottom); // 下移的边界
        }
      } else {
        finalY = (viewportHeight - scaledHeight) / 2; // 居中
      }
      // --- 边界限制逻辑结束 ---
      setOffsetX(finalX);
      setOffsetY(finalY);
    };

    // 结束平移
    const handlePanEnd = () => {
      // 修复：在拖动结束后，将当前视觉上的偏移量"提交"为下一次平移的基准
      if (isDragging) {
        committedOffsetXRef.current = offsetX;
        committedOffsetYRef.current = offsetY;
      }
      setIsDragging(false);
      isMouseDownRef.current = false;
    };

    // Effect to load processed image data when a new image or url is provided
    useEffect(() => {
      if (!originalImgObject) {
        setProcessedData(null);
        setProcessedImageObj(null);
        return;
      }

      // 优先从 processedUrl prop 加载
      if (processedUrl) {
        const img = new Image();
        // 对于 blob URL，不需要设置 crossOrigin
        if (!processedUrl.startsWith('blob:')) {
          img.crossOrigin = 'anonymous';
        }
        img.onload = () => {
          console.log('处理后的图片加载成功:', processedUrl);
          setProcessedImageObj(img);
          setProcessedData(null); // 清空旧的 ZIP 数据
        };
        img.onerror = e => {
          console.error('加载处理后的图片失败:', processedUrl, e);
          setProcessedImageObj(null);
        };
        img.src = processedUrl;
      }
      // 其次从 mockImagePaths prop 加载（用于测试）
      else if (
        mockImagePaths &&
        mockImagePaths.colorUrl &&
        mockImagePaths.alphaUrl
      ) {
        loadDirectImages(mockImagePaths)
          .then(setProcessedData)
          .catch(error => {
            console.error('从 mockImagePaths 加载直接图像时出错:', error);
            setProcessedData(null);
          });
        setProcessedImageObj(null);
      } else {
        setProcessedData(null);
        setProcessedImageObj(null);
      }
    }, [originalImgObject, processedUrl, mockImagePaths, loadDirectImages]);

    // 加载背景图片对象
    useEffect(() => {
      if (backgroundImageUrl) {
        const img = new Image();
        img.crossOrigin = 'Anonymous';
        img.onload = () => {
          setBackgroundImageObj(img);
        };
        img.onerror = () => {
          console.error(`加载背景图片失败: ${backgroundImageUrl}`);
          setBackgroundImageObj(null);
        };
        img.src = backgroundImageUrl;
      } else {
        setBackgroundImageObj(null);
      }
    }, [backgroundImageUrl]);

    // 绘制背景层（棋盘格、纯色或背景图）的函数
    const drawBackgroundLayer = useCallback(
      (
        canvas: HTMLCanvasElement,
        canvasWidth: number,
        canvasHeight: number
      ) => {
        if (!canvas) return;
        const ctx = canvas.getContext('2d')!;
        const TILE_SIZE = 10; // 棋盘格的大小

        const originalFilter = ctx.filter;
        ctx.clearRect(0, 0, canvasWidth, canvasHeight);

        // 如果启用，应用模糊效果
        if (isBlurEnabled && blurAmount > 0) {
          ctx.filter = `blur(${blurAmount}px)`;
        } else {
          ctx.filter = 'none';
        }

        if (backgroundImageObj) {
          const imgAspect =
            backgroundImageObj.naturalWidth / backgroundImageObj.naturalHeight;
          const canvasAspect = canvasWidth / canvasHeight;
          let sx, sy, sWidth, sHeight;

          if (imgAspect > canvasAspect) {
            sHeight = backgroundImageObj.naturalHeight;
            sWidth = sHeight * canvasAspect;
            sx = (backgroundImageObj.naturalWidth - sWidth) / 2;
            sy = 0;
          } else {
            sWidth = backgroundImageObj.naturalWidth;
            sHeight = sWidth / canvasAspect;
            sx = 0;
            sy = (backgroundImageObj.naturalHeight - sHeight) / 2;
          }
          ctx.drawImage(
            backgroundImageObj,
            sx,
            sy,
            sWidth,
            sHeight,
            0,
            0,
            canvasWidth,
            canvasHeight
          );
        } else if (backgroundColor && backgroundColor !== 'transparent') {
          ctx.fillStyle = backgroundColor;
          ctx.fillRect(0, 0, canvasWidth, canvasHeight);
        } else {
          // 绘制棋盘格背景
          const patternCanvas = document.createElement('canvas');
          patternCanvas.width = TILE_SIZE * 2;
          patternCanvas.height = TILE_SIZE * 2;
          const patternCtx = patternCanvas.getContext('2d')!;

          patternCtx.fillStyle = '#fafafa';
          patternCtx.fillRect(0, 0, TILE_SIZE * 2, TILE_SIZE * 2);
          patternCtx.fillStyle = '#f0f0f0';
          patternCtx.fillRect(0, 0, TILE_SIZE, TILE_SIZE);
          patternCtx.fillRect(TILE_SIZE, TILE_SIZE, TILE_SIZE, TILE_SIZE);

          const pattern = ctx.createPattern(patternCanvas, 'repeat');
          if (pattern) {
            ctx.fillStyle = pattern;
            ctx.fillRect(0, 0, canvasWidth, canvasHeight);
          }
        }
        ctx.filter = originalFilter;
      },
      [backgroundColor, backgroundImageObj, isBlurEnabled, blurAmount]
    );

    // 绘制处理后的图像（应用 alpha 蒙版）
    const drawProcessedImage = useCallback(
      (
        ctx: CanvasRenderingContext2D,
        x: number,
        y: number,
        width: number,
        height: number
      ) => {
        // 优先使用处理后的图像对象
        if (processedImageObj) {
          ctx.drawImage(processedImageObj, x, y, width, height);
          return;
        }

        // 回退到 ZIP 数据处理（兼容性）
        if (!processedData || !originalImgObject) return;
        const offscreenCanvas = document.createElement('canvas');
        offscreenCanvas.width = originalImgObject.naturalWidth;
        offscreenCanvas.height = originalImgObject.naturalHeight;
        const offscreenCtx = offscreenCanvas.getContext('2d')!;

        offscreenCtx.drawImage(
          processedData.colorImage,
          0,
          0,
          originalImgObject.naturalWidth,
          originalImgObject.naturalHeight
        );

        const imageData = offscreenCtx.getImageData(
          0,
          0,
          offscreenCanvas.width,
          offscreenCanvas.height
        );

        const alphaCanvas = document.createElement('canvas');
        alphaCanvas.width = originalImgObject.naturalWidth;
        alphaCanvas.height = originalImgObject.naturalHeight;
        const alphaCtx = alphaCanvas.getContext('2d')!;

        alphaCtx.drawImage(
          processedData.alphaImage,
          0,
          0,
          originalImgObject.naturalWidth,
          originalImgObject.naturalHeight
        );
        const alphaData = alphaCtx.getImageData(
          0,
          0,
          alphaCanvas.width,
          alphaCanvas.height
        );

        // 将 alpha 数据应用到主图像数据
        for (let i = 0; i < imageData.data.length; i += 4) {
          if (i < alphaData.data.length)
            imageData.data[i + 3] = alphaData.data[i];
        }
        offscreenCtx.putImageData(imageData, 0, 0);
        ctx.drawImage(offscreenCanvas, x, y, width, height);
      },
      [processedImageObj, processedData, originalImgObject]
    );

    // 渲染主图像层
    const renderBaseLayerCanvas = useCallback(() => {
      const canvas = baseLayerCanvasRef.current;
      if (!canvas || !originalImgObject) {
        return;
      }

      const currentScaleProp = scale;
      const imgNaturalWidth = originalImgObject.naturalWidth;
      const imgNaturalHeight = originalImgObject.naturalHeight;

      const targetDrawWidth = imgNaturalWidth * currentScaleProp;
      const targetDrawHeight = imgNaturalHeight * currentScaleProp;

      if (
        Math.abs(canvas.width - targetDrawWidth) > 1 ||
        Math.abs(canvas.height - targetDrawHeight) > 1
      ) {
        // 这是一个警告，指出 canvas 的 HTML 属性尺寸与基于 scale 计算的绘制尺寸不完全匹配，
        // 可能是由于浮点数精度问题或状态同步延迟，通常不影响功能。
      }

      const ctx = canvas.getContext('2d')!;
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      if (processedImageObj || processedData) {
        drawProcessedImage(ctx, 0, 0, targetDrawWidth, targetDrawHeight);
      } else {
        ctx.drawImage(
          originalImgObject,
          0,
          0,
          targetDrawWidth,
          targetDrawHeight
        );
      }
    }, [
      originalImgObject,
      processedImageObj,
      processedData,
      drawProcessedImage,
      scale,
    ]);

    // 渲染对比模式下的擦除层
    const renderCompareWipeCanvas = useCallback(() => {
      const canvas = compareWipeCanvasRef.current;
      if (!canvas || !originalImgObject) return;

      const scaledWidth = originalImgObject.naturalWidth * scale;
      const scaledHeight = originalImgObject.naturalHeight * scale;

      const ctx = canvas.getContext('2d')!;
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      if (compareProgress > 0) {
        ctx.save();
        ctx.beginPath();
        ctx.rect(0, 0, canvas.width * compareProgress, canvas.height);
        ctx.clip();
        ctx.drawImage(originalImgObject, 0, 0, scaledWidth, scaledHeight);
        ctx.restore();
      }
    }, [originalImgObject, compareProgress, scale]);

    // Effect for rendering the main image layer
    useEffect(() => {
      if (!originalImgObject) return;
      renderBaseLayerCanvas();
      if (compareProgress > 0) {
        renderCompareWipeCanvas();
      }
    }, [
      originalImgObject,
      processedImageObj,
      processedData,
      compareProgress,
      scale,
      renderBaseLayerCanvas,
      renderCompareWipeCanvas,
    ]);

    // Effect for rendering the background layer
    useEffect(() => {
      const bgCanvas = backgroundCanvasRef.current;

      if (
        !bgCanvas ||
        !originalImgObject ||
        !containerWidth ||
        !containerHeight
      ) {
        if (bgCanvas) {
          const ctx = bgCanvas.getContext('2d');
          if (ctx) {
            bgCanvas.width = containerWidth || bgCanvas.width;
            bgCanvas.height = containerHeight || bgCanvas.height;
            ctx.clearRect(0, 0, bgCanvas.width, bgCanvas.height);
          }
        }
        return;
      }

      // 使用传入的容器尺寸而不是实际的viewport尺寸，确保背景棋盘保持固定尺寸
      bgCanvas.width = containerWidth;
      bgCanvas.height = containerHeight;

      drawBackgroundLayer(bgCanvas, containerWidth, containerHeight);
    }, [
      originalImgObject,
      backgroundColor,
      backgroundImageObj,
      drawBackgroundLayer,
      containerWidth,
      containerHeight,
      isBlurEnabled,
      blurAmount,
    ]);

    // Effect for handling window resize
    useEffect(() => {
      if (!originalImgObject || !containerWidth || !containerHeight) return;

      const handleResize = () => {
        renderBaseLayerCanvas();
        if (compareProgress > 0) {
          renderCompareWipeCanvas();
        }

        const bgCanvas = backgroundCanvasRef.current;
        if (bgCanvas) {
          // 保持使用传入的容器尺寸，而不是实际的viewport尺寸
          bgCanvas.width = containerWidth;
          bgCanvas.height = containerHeight;
          drawBackgroundLayer(bgCanvas, containerWidth, containerHeight);
        }
      };

      window.addEventListener('resize', handleResize);
      setTimeout(handleResize, 0);

      return () => window.removeEventListener('resize', handleResize);
    }, [
      originalImgObject,
      processedImageObj,
      processedData,
      compareProgress,
      renderBaseLayerCanvas,
      renderCompareWipeCanvas,
      scale,
      backgroundColor,
      backgroundImageObj,
      drawBackgroundLayer,
      containerWidth,
      containerHeight,
      isBlurEnabled,
      blurAmount,
    ]);

    // Effect for handling compare wipe animation
    useEffect(() => {
      const animate = () => {
        setCompareProgress(prevProgress => {
          let newProgress;
          if (isCompareModeActive) {
            newProgress = Math.min(1, prevProgress + 0.05);
          } else {
            newProgress = Math.max(0, prevProgress - 0.05);
          }

          if (
            (isCompareModeActive && newProgress < 1) ||
            (!isCompareModeActive && newProgress > 0)
          ) {
            animationFrameRef.current = requestAnimationFrame(animate);
          } else {
            animationFrameRef.current = null;
          }
          return newProgress;
        });
      };

      if (originalImgObject) {
        if (
          isCompareModeActive ||
          (!isCompareModeActive && compareProgress > 0)
        ) {
          animationFrameRef.current = requestAnimationFrame(animate);
        } else {
          if (animationFrameRef.current) {
            cancelAnimationFrame(animationFrameRef.current);
            animationFrameRef.current = null;
          }
        }
      } else {
        if (animationFrameRef.current) {
          cancelAnimationFrame(animationFrameRef.current);
          animationFrameRef.current = null;
        }
      }

      return () => {
        if (animationFrameRef.current) {
          cancelAnimationFrame(animationFrameRef.current);
          animationFrameRef.current = null;
        }
      };
    }, [isCompareModeActive, compareProgress, originalImgObject]);

    // 初始化橡皮擦图层
    // 用于跟踪是否已经初始化过橡皮擦图层
    const isEraseLayerInitializedRef = useRef(false);

    useEffect(() => {
      if (originalImgObject && !isEraseLayerInitializedRef.current) {
        resetEraseLayer();
        isEraseLayerInitializedRef.current = true;
      }

      // 当图片变化时重置初始化标志
      return () => {
        if (prevOriginalImgObjectRef.current !== originalImgObject) {
          isEraseLayerInitializedRef.current = false;
        }
      };
    }, [originalImgObject, resetEraseLayer]);

    // 当缩放改变时重新设置橡皮擦图层尺寸
    // 用于存储橡皮擦数据的 ref，避免在缩放时触发无限循环
    const savedEraseDataRef = useRef<ImageData | null>(null);

    useEffect(() => {
      if (
        originalImgObject &&
        eraseCanvasRef.current &&
        isEraseLayerInitializedRef.current
      ) {
        const canvas = eraseCanvasRef.current;
        const scaledWidth = originalImgObject.naturalWidth * scale;
        const scaledHeight = originalImgObject.naturalHeight * scale;

        if (canvas.width !== scaledWidth || canvas.height !== scaledHeight) {
          // 保存当前内容
          const ctx = canvas.getContext('2d');
          if (ctx) {
            // 首先检查是否有保存的橡皮擦数据
            let dataToRestore = savedEraseDataRef.current;

            // 如果没有保存的数据，尝试从当前画布获取
            if (!dataToRestore) {
              const currentImageData = ctx.getImageData(
                0,
                0,
                canvas.width,
                canvas.height
              );

              // 检查画布是否有内容（不是完全透明）
              const hasContent = Array.from(currentImageData.data).some(
                (value, index) => index % 4 === 3 && value > 0 // 检查 alpha 通道
              );

              if (hasContent) {
                // 将当前缩放的橡皮擦数据转换为原始尺寸
                const originalSizeCanvas = document.createElement('canvas');
                originalSizeCanvas.width = originalImgObject.naturalWidth;
                originalSizeCanvas.height = originalImgObject.naturalHeight;
                const originalSizeCtx = originalSizeCanvas.getContext('2d');

                if (originalSizeCtx) {
                  const tempCanvas = document.createElement('canvas');
                  tempCanvas.width = currentImageData.width;
                  tempCanvas.height = currentImageData.height;
                  const tempCtx = tempCanvas.getContext('2d');

                  if (tempCtx) {
                    tempCtx.putImageData(currentImageData, 0, 0);
                    originalSizeCtx.drawImage(
                      tempCanvas,
                      0,
                      0,
                      tempCanvas.width,
                      tempCanvas.height,
                      0,
                      0,
                      originalSizeCanvas.width,
                      originalSizeCanvas.height
                    );

                    dataToRestore = originalSizeCtx.getImageData(
                      0,
                      0,
                      originalSizeCanvas.width,
                      originalSizeCanvas.height
                    );
                    savedEraseDataRef.current = dataToRestore;
                  }
                }
              }
            }

            // 调整画布尺寸
            canvas.width = scaledWidth;
            canvas.height = scaledHeight;

            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 如果有数据需要恢复，则恢复到新的尺寸
            if (dataToRestore) {
              const tempCanvas = document.createElement('canvas');
              tempCanvas.width = dataToRestore.width;
              tempCanvas.height = dataToRestore.height;
              const tempCtx = tempCanvas.getContext('2d');

              if (tempCtx) {
                tempCtx.putImageData(dataToRestore, 0, 0);
                ctx.drawImage(
                  tempCanvas,
                  0,
                  0,
                  tempCanvas.width,
                  tempCanvas.height,
                  0,
                  0,
                  scaledWidth,
                  scaledHeight
                );
              }
            }
          }
        }
      }
    }, [originalImgObject, scale]);

    return (
      <div // 视口容器
        ref={containerRef}
        className={`relative bg-gray-100 rounded-lg transition-all duration-200 overflow-hidden ${
          isEraseMode || isRestoreMode ? 'cursor-crosshair' : 'cursor-default'
        }`}
        style={dynamicContainerStyle}
        onTouchStart={e => {
          handleTouchStart(e.nativeEvent);
        }}
        onTouchMove={e => {
          handleTouchMove(e.nativeEvent);
        }}
        onTouchEnd={() => {
          handleTouchEnd();
        }}
      >
        {originalImgObject && (
          <>
            {/* 背景图层 */}
            <div className='absolute top-0 left-0 w-full h-full'>
              <canvas
                ref={backgroundCanvasRef}
                className='absolute top-0 left-0 w-full h-full'
                style={{ zIndex: 0 }}
              />
            </div>

            {/* 可移动的图像图层容器 */}
            <div
              className='absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2'
              style={{
                width: originalImgObject.naturalWidth * scale,
                height: originalImgObject.naturalHeight * scale,
                transform: `translate3d(${offsetX}px, ${offsetY}px, 0)`,
                willChange: 'transform',
                backfaceVisibility: 'hidden', // 添加这个
                perspective: '1000px', // 添加这个
                transformStyle: 'preserve-3d', // 添加这个
                backgroundColor: 'rgba(0,255,0,0.1)',
              }}
            >
              {/* Canvas 容器 */}
              <div
                style={{
                  width: '100%',
                  height: '100%',
                  position: 'relative',
                }}
              >
                <canvas
                  ref={baseLayerCanvasRef}
                  width={originalImgObject.naturalWidth * scale}
                  height={originalImgObject.naturalHeight * scale}
                  className='absolute top-0 left-0 w-full h-full'
                  style={{
                    zIndex: 1,
                    imageRendering: 'crisp-edges',
                    backfaceVisibility: 'hidden',
                    willChange: 'transform', // 添加这个
                    transform: 'translateZ(0)', // 添加这个
                  }}
                />
                {compareProgress > 0 && (
                  <canvas
                    ref={compareWipeCanvasRef}
                    width={originalImgObject.naturalWidth * scale}
                    height={originalImgObject.naturalHeight * scale}
                    className='absolute top-0 left-0 w-full h-full'
                    style={{
                      zIndex: 3,
                      imageRendering: 'crisp-edges',
                      backfaceVisibility: 'hidden',
                    }}
                  />
                )}
                {/* 橡皮擦图层 */}
                <canvas
                  ref={eraseCanvasRef}
                  width={originalImgObject.naturalWidth * scale}
                  height={originalImgObject.naturalHeight * scale}
                  className='absolute top-0 left-0 w-full h-full'
                  style={{
                    zIndex: 2,
                    imageRendering: 'crisp-edges',
                    backfaceVisibility: 'hidden',
                  }}
                />
              </div>
            </div>

            {/* 橡皮擦/恢复预览光圈 */}
            {(isEraseMode || isRestoreMode) && (
              <div
                ref={cursorElementRef}
                className='absolute pointer-events-none rounded-full'
                style={{
                  left: 0,
                  top: 0,
                  width: eraseBrushSize,
                  height: eraseBrushSize,
                  zIndex: 10,
                  backgroundColor: isRestoreMode ? '#00ff00' : '#ff0000', // 恢复模式绿色，橡皮擦红色
                  opacity: isErasing ? 0.5 : 0.7, // 擦除时稍微透明一些
                  transform: 'translate3d(-9999px, -9999px, 0)', // 初始位置在屏幕外
                  willChange: 'transform', // 优化动画性能
                  display: 'none', // 默认隐藏
                }}
              />
            )}
          </>
        )}
      </div>
    );
  }
);

MobileCanvasImageEditor.displayName = 'MobileCanvasImageEditor';
/**
 * 用于处理图像数据的自定义 Hook。
 */
function useImageProcessing() {
  /**
   * 从给定的 URL 加载图像。
   */
  const loadDirectImages = useCallback(
    async (paths: {
      colorUrl: string;
      alphaUrl: string;
    }): Promise<ProcessedImageData> => {
      const colorImg = new Image();
      const alphaImg = new Image();

      colorImg.crossOrigin = 'Anonymous';
      alphaImg.crossOrigin = 'Anonymous';

      return new Promise((resolve, reject) => {
        let loadedCount = 0;
        const checkComplete = () => {
          if (loadedCount === 2) {
            resolve({ colorImage: colorImg, alphaImage: alphaImg });
          }
        };
        colorImg.onload = () => {
          loadedCount++;
          checkComplete();
        };
        alphaImg.onload = () => {
          loadedCount++;
          checkComplete();
        };
        colorImg.onerror = e =>
          reject(new Error(`从 ${paths.colorUrl} 加载颜色图像失败: ${e}`));
        alphaImg.onerror = e =>
          reject(new Error(`从 ${paths.alphaUrl} 加载alpha图像失败: ${e}`));
        colorImg.src = paths.colorUrl;
        alphaImg.src = paths.alphaUrl;
      });
    },
    []
  );
  return { loadDirectImages };
}
