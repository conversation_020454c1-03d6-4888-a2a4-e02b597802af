# API 接口使用指南

本项目的 API 接口架构包含以下部分：

## 📁 文件结构

```
src/lib/api/
├── http-client.ts    # HTTP 客户端（带拦截器）
├── auth.ts          # 认证相关接口
├── image.ts         # 图片处理接口
├── index.ts         # 统一导出
└── README.md        # 使用指南
```

## 🔧 配置

### 环境变量
在 `.env.local` 文件中添加以下配置：

```bash
# API 配置
NEXT_PUBLIC_API_BASE_URL=https://your-backend-api.com/api
PHOTOROOM_API_KEY=your-photoroom-api-key
```

## 📖 使用方式

### 1. 在组件中使用 API

```tsx
import { login, uploadImage, removeBackground } from '@/lib/api';

// 直接调用函数
const response = await login({ username, password });
const uploadResult = await uploadImage(file);
const blob = await removeBackground({ image: base64Image });
```

### 2. 登录示例

```tsx
'use client';

import { useState } from 'react';
import { login } from '@/lib/api';

export function LoginExample() {
  const [loading, setLoading] = useState(false);

  const handleLogin = async () => {
    try {
      setLoading(true);
      const response = await login({
        username: '<EMAIL>',
        password: 'password123'
      });
      
      console.log('登录成功:', response.userInfo);
      // token 已自动保存到 localStorage
      
    } catch (error) {
      console.error('登录失败:', error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <button onClick={handleLogin} disabled={loading}>
      {loading ? '登录中...' : '登录'}
    </button>
  );
}
```

### 3. 图片处理示例

```tsx
import { uploadImage, removeBackground, getImageList } from '@/lib/api';

// 上传图片
const uploadResult = await uploadImage(file, 'category');

// 去除背景
const blob = await removeBackground({ image: base64Image });

// 获取图片列表
const imageList = await getImageList({ page: 1, pageSize: 10 });
```

### 4. 错误处理

```tsx
try {
  const data = await login(credentials);
} catch (error) {
  // 所有错误都会抛出后端返回的 msg
  console.error('请求失败:', error.message);
  // 显示错误提示
  showErrorMessage(error.message);
}
```

## 🔐 认证机制

### Token 管理
- 登录成功后，token 自动保存到 `localStorage` 和 `cookie`
- 所有需要认证的请求自动添加 `Authorization: Bearer {token}` 头
- token 失效时，自动清除本地存储并跳转到登录页

### 自动错误处理
HTTP 客户端的响应拦截器会自动处理以下情况：

- **成功 (code = 1)**: 返回 data 数据
- **Token 失效 (code = 200015)**: 自动清除 token，跳转登录页并保留当前路径
- **其他错误 (code ≠ 1)**: 直接抛出后端返回的 msg

### 中间件保护
项目根目录的 `middleware.ts` 文件提供了**轻量级**的路由保护：

- 受保护的路由：检查是否有 token（不验证有效性）
- 认证路由：有 token 时自动跳转到 dashboard
- **Token 有效性完全由 API 响应拦截器处理**

## 🎯 错误处理原则

### 简化的错误处理
- **只关注后端 code 是否为 1**
- **所有非 1 的情况统一抛出**
- **错误消息直接使用后端返回的 msg**
- **特殊处理 200015 (Token失效)**

```tsx
// 响应拦截器逻辑
if (data.code === 1) {
  return data.data;  // 成功，返回数据
}

if (data.code === 200015) {
  // Token 失效，自动跳转登录页
  handleTokenExpired();
  throw new Error(data.msg || 'Token已失效，请重新登录');
}

// 其他所有错误，直接抛出后端消息
throw new Error(data.msg || '请求失败');
```

## 🎯 最佳实践

### 1. 接口分类
按业务模块创建不同的 API 文件：
- `auth.ts` - 认证相关
- `user.ts` - 用户管理
- `image.ts` - 图片处理
- `order.ts` - 订单管理

### 2. 类型定义
为每个接口定义清晰的 TypeScript 类型：

```tsx
export interface CreateUserRequest {
  username: string;
  email: string;
  password: string;
}

export interface UserInfo {
  id: string;
  username: string;
  email: string;
  avatar?: string;
}
```

### 3. 错误处理策略
- **Token 失效**: 由响应拦截器自动处理，无需手动处理
- **业务错误**: 直接显示后端返回的错误消息
- **网络错误**: 统一显示网络错误提示

### 4. 请求拦截
- 自动添加认证头
- 设置请求超时

### 5. 响应拦截
- 简单的成功/失败判断 (code === 1)
- 自动 Token 失效处理 (code === 200015)
- 直接使用后端错误消息

## 🔄 扩展新接口

当需要添加新的业务接口时：

1. 在 `src/lib/api/` 下创建新的模块文件
2. 定义接口类型
3. 创建 API 函数
4. 在 `index.ts` 中导出

```tsx
// src/lib/api/product.ts
export async function getProductList(): Promise<Product[]> {
  return httpClient.get('/products');
}

export async function getProductDetail(id: string): Promise<Product> {
  return httpClient.get(`/products/${id}`);
}

// src/lib/api/index.ts
export * from './product';
```

## 💡 核心优势

✅ **极简的错误处理**: 只关注 code 是否为 1，其他全部统一抛出  
✅ **自动化 Token 管理**: Token 失效自动跳转，无需手动处理  
✅ **类型安全**: 完整的 TypeScript 类型定义  
✅ **后端消息优先**: 直接使用后端返回的错误消息  
✅ **简单易用**: 直接函数调用，无复杂封装  
✅ **可维护性**: 模块化设计，易于扩展和维护
