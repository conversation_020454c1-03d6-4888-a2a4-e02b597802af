# Husky 配置说明

本项目已经配置了 Husky Git hooks，用于确保代码质量和规范化提交。

## 已配置的 Git Hooks

### 1. pre-commit（提交前检查）
- **作用**: 在每次 `git commit` 之前自动运行
- **执行内容**: `npm run lint`
- **目的**: 确保提交的代码符合 ESLint 规范

### 2. commit-msg（提交信息检查）
- **作用**: 检查提交信息是否符合规范
- **格式要求**: `<type>(<scope>): <description>`
- **支持的类型**:
  - `feat`: 新功能
  - `fix`: 修复bug
  - `docs`: 文档更新
  - `style`: 代码格式化
  - `refactor`: 重构
  - `test`: 测试相关
  - `chore`: 构建过程或辅助工具的变动

**提交信息示例**:
```
feat(ui): 添加新的按钮组件
fix(api): 修复用户登录问题
docs: 更新README文档
```

### 3. pre-push（推送前检查）
- **作用**: 在每次 `git push` 之前自动运行
- **执行内容**: `npm run build`
- **目的**: 确保推送的代码能够成功构建

## 使用方法

1. **正常提交代码**:
   ```bash
   git add .
   git commit -m "feat(component): 添加新的图片处理组件"
   ```

2. **如果代码不符合规范**:
   - pre-commit 会阻止提交并显示错误信息
   - 修复错误后重新提交

3. **如果提交信息格式不正确**:
   - commit-msg 会显示正确的格式要求
   - 使用正确格式重新提交

4. **推送代码**:
   ```bash
   git push
   ```
   - pre-push 会自动运行构建检查

## 跳过钩子（仅在紧急情况下使用）

如果需要跳过钩子检查，可以使用：
```bash
git commit --no-verify -m "紧急修复"
git push --no-verify
```

**注意**: 只在紧急情况下使用 `--no-verify`，因为这会跳过所有质量检查。 