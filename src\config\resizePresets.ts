// 预设类型定义
export interface FixedPreset {
  id: string;
  name: string;
  width: number;
  height: number;
  icon: string;
  mode: 'fixed';
}

export interface RatioPreset {
  id: string;
  name: string;
  aspectRatio: number;
  icon: string;
  mode: 'ratio';
}

export type ResizePreset = FixedPreset | RatioPreset;

export interface ResizePresetCategory {
  title: string;
  presets: ResizePreset[];
}

export type ResizePresetCategories = {
  marketplace: ResizePresetCategory;
  social: ResizePresetCategory;
  ratio: ResizePresetCategory;
  custom: ResizePresetCategory;
  original: ResizePresetCategory;
};

// 平台尺寸预设数据
export const RESIZE_PRESETS: ResizePresetCategories = {
  marketplace: {
    title: 'For Marketplace Platforms',
    presets: [
      {
        id: 'tiktok',
        name: 'TikTok',
        width: 1600,
        height: 1600,
        icon: '/apps/images/platform/tiktok.png',
        mode: 'fixed',
      },
      {
        id: 'amazon',
        name: 'Amazon',
        width: 2000,
        height: 2000,
        icon: '/apps/images/platform/amazon.png',
        mode: 'fixed',
      },
      {
        id: 'ebay',
        name: 'eBay',
        width: 1600,
        height: 1600,
        icon: '/apps/images/platform/ebay.png',
        mode: 'fixed',
      },
      {
        id: 'postmark',
        name: 'Postmark',
        width: 1080,
        height: 1080,
        icon: '/apps/images/platform/postmark.png',
        mode: 'fixed',
      },
      {
        id: 'depop',
        name: 'Depop',
        width: 1280,
        height: 1280,
        icon: '/apps/images/platform/depop.png',
        mode: 'fixed',
      },
      {
        id: 'mercari',
        name: 'Mercari',
        width: 1080,
        height: 1080,
        icon: '/apps/images/platform/mercari.png',
        mode: 'fixed',
      },
      {
        id: 'mercado-libre',
        name: 'Mercado Libre',
        width: 1080,
        height: 1080,
        icon: '/apps/images/platform/mercadoLibre.png',
        mode: 'fixed',
      },
      {
        id: 'shopee',
        name: 'Shopee',
        width: 1080,
        height: 1080,
        icon: '/apps/images/platform/shopee.png',
        mode: 'fixed',
      },
      {
        id: 'shopify-square',
        name: 'Shopify square',
        width: 2048,
        height: 2048,
        icon: '/apps/images/platform/shopifyPortrait.png',
        mode: 'fixed',
      },
      {
        id: 'shopify-landscape',
        name: 'Shopify landscape',
        width: 2000,
        height: 1800,
        icon: '/apps/images/platform/shopifyPortrait.png',
        mode: 'fixed',
      },
      {
        id: 'shopify-portrait',
        name: 'Shopify portrait',
        width: 1600,
        height: 2000,
        icon: '/apps/images/platform/shopifyPortrait.png',
        mode: 'fixed',
      },
      {
        id: 'lazada',
        name: 'Lazada',
        width: 1080,
        height: 1080,
        icon: '/apps/images/platform/lazada.png',
        mode: 'fixed',
      },
      {
        id: 'etsy',
        name: 'Etsy',
        width: 2700,
        height: 2025,
        icon: '/apps/images/platform/etsy.png',
        mode: 'fixed',
      },
      {
        id: 'vinted',
        name: 'Vinted',
        width: 800,
        height: 600,
        icon: '/apps/images/platform/vinted.png',
        mode: 'fixed',
      },
    ],
  },
  social: {
    title: 'For Social Media Platforms',
    presets: [
      {
        id: 'ins-story',
        name: 'Instagram story',
        width: 1080,
        height: 1920,
        icon: '/apps/images/platform/ins.png',
        mode: 'fixed',
      },
      {
        id: 'ins-post',
        name: 'Instagram post',
        width: 1080,
        height: 1080,
        icon: '/apps/images/platform/ins.png',
        mode: 'fixed',
      },
      {
        id: 'fb-cover',
        name: 'Facebook cover',
        width: 820,
        height: 312,
        icon: '/apps/images/platform/facebook.png',
        mode: 'fixed',
      },
      {
        id: 'fb-post',
        name: 'Facebook post',
        width: 1200,
        height: 628,
        icon: '/apps/images/platform/facebook.png',
        mode: 'fixed',
      },
      {
        id: 'fb-marketplace',
        name: 'Facebook Marketplace',
        width: 1200,
        height: 628,
        icon: '/apps/images/platform/facebook.png',
        mode: 'fixed',
      },
      {
        id: 'tiktok-post',
        name: 'TikTok post',
        width: 1080,
        height: 1080,
        icon: '/apps/images/platform/tiktok.png',
        mode: 'fixed',
      },
      {
        id: 'tiktok-cover',
        name: 'TikTok cover',
        width: 1080,
        height: 1440,
        icon: '/apps/images/platform/tiktok.png',
        mode: 'fixed',
      },
      {
        id: 'youtube-cover',
        name: 'YouTube cover',
        width: 1280,
        height: 1920,
        icon: '/apps/images/platform/youtube.png',
        mode: 'fixed',
      },
      {
        id: 'youtube-channel',
        name: 'YouTube channel',
        width: 2560,
        height: 1440,
        icon: '/apps/images/platform/youtube.png',
        mode: 'fixed',
      },
      {
        id: 'twitter-cover',
        name: 'X cover',
        width: 2560,
        height: 1440,
        icon: '/apps/images/platform/x.png',
        mode: 'fixed',
      },
      {
        id: 'twitter-post',
        name: 'X post',
        width: 2560,
        height: 1440,
        icon: '/apps/images/platform/x.png',
        mode: 'fixed',
      },
    ],
  },
  ratio: {
    title: 'Ratio Size',
    presets: [
      {
        id: 'square',
        name: 'Square(1:1)',
        aspectRatio: 1, // 1:1
        icon: '⬜',
        mode: 'ratio',
      },
      {
        id: 'ratio-3-2',
        name: '3:2',
        aspectRatio: 3 / 2, // 1.5
        icon: '📐',
        mode: 'ratio',
      },
      {
        id: 'ratio-4-3',
        name: '4:3',
        aspectRatio: 4 / 3, // 1.333...
        icon: '📐',
        mode: 'ratio',
      },
      {
        id: 'ratio-5-3',
        name: '5:3',
        aspectRatio: 5 / 3, // 1.666...
        icon: '📐',
        mode: 'ratio',
      },
      {
        id: 'ratio-5-4',
        name: '5:4',
        aspectRatio: 5 / 4, // 1.25
        icon: '📐',
        mode: 'ratio',
      },
      {
        id: 'ratio-7-5',
        name: '7:5',
        aspectRatio: 7 / 5, // 1.4
        icon: '📐',
        mode: 'ratio',
      },
      {
        id: 'ratio-16-9',
        name: '16:9',
        aspectRatio: 16 / 9, // 1.777...
        icon: '📐',
        mode: 'ratio',
      },
      {
        id: 'passport-2x2',
        name: 'Passport & Photo(2 x 2in)',
        width: 600,
        height: 600,
        icon: '🆔',
        mode: 'fixed',
      },
      {
        id: 'passport-3.5x4.5',
        name: 'Passport & Photo(3.5 x 4.5in)',
        width: 1050,
        height: 1350,
        icon: '🆔',
        mode: 'fixed',
      },
    ],
  },
  custom: {
    title: 'Custom Size',
    presets: [], // Custom Size 不需要预设列表
  },
  original: {
    title: 'Original Size',
    presets: [], // Original Size 不需要预设列表
  },
};
