/**
 * 图片压缩工具函数
 * 支持按压缩程度和自定义体积两种压缩方式
 * 支持 PNG 无损压缩
 */

import type { SupportedFormat } from './imageConvert';
import UPNG from 'upng-js';

// 压缩程度选项
export type CompressionLevel = 'original' | 'light' | 'medium' | 'deep';

// 压缩设置接口
export interface CompressionSettings {
  level?: CompressionLevel;
  customSize?: number; // KB
  customUnit?: 'KB' | 'MB';
}

// 压缩结果接口
export interface CompressionResult {
  compressedUrl: string;
  originalSize: number; // bytes
  compressedSize: number; // bytes
  compressionRatio: number; // 压缩比例 (0-1)
}

/**
 * 获取图片文件大小（字节）
 * @param dataUrl base64 数据URL
 * @returns 文件大小（字节）
 */
export function getImageSizeFromDataUrl(dataUrl: string): number {
  // 移除 data:image/xxx;base64, 前缀
  const base64Data = dataUrl.split(',')[1];
  if (!base64Data) return 0;

  // Base64 编码后的大小约为原始大小的 4/3
  // 但需要考虑填充字符
  const padding = (base64Data.match(/=/g) || []).length;
  return Math.floor((base64Data.length * 3) / 4) - padding;
}

/**
 * 将字节转换为可读格式
 * @param bytes 字节数
 * @returns 格式化的大小字符串
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

/**
 * 根据压缩程度获取质量参数
 * @param level 压缩程度
 * @returns 质量参数 (0-1)
 */
export function getQualityByLevel(level: CompressionLevel): number {
  switch (level) {
    case 'original':
      return 1.0; // 原始质量
    case 'light':
      return 0.9; // 轻度压缩（最清晰）
    case 'medium':
      return 0.7; // 中度压缩（品质均衡）
    case 'deep':
      return 0.5; // 深度压缩（最小体积）
    default:
      return 0.9;
  }
}

/**
 * 压缩 PNG 图片使用 UPNG.js（无损压缩）
 * @param imageUrl 图片URL
 * @param quality 质量参数 (0-1)，对于PNG转换为色彩数量
 * @returns Promise<string> 压缩后的base64数据URL
 */
async function compressPngWithUPNG(
  imageUrl: string,
  quality: number = 1.0
): Promise<string> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';

    img.onload = () => {
      try {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        if (!ctx) {
          reject(new Error('无法获取Canvas上下文'));
          return;
        }

        canvas.width = img.width;
        canvas.height = img.height;
        ctx.drawImage(img, 0, 0);

        // 获取图像数据
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const rgba = new Uint8Array(imageData.data.buffer);

        // 根据质量参数确定颜色数量
        // quality 越大越高清，0为无损
        let colors = 256;
        if (quality <= 0.5) {
          colors = 235;
        } else if (quality <= 0.7) {
          colors = 245;
        } else if (quality <= 0.9) {
          colors = 255;
        }

        // 使用 UPNG 压缩
        const compressed = UPNG.encode(
          [rgba.buffer as ArrayBuffer],
          canvas.width,
          canvas.height,
          colors
        );

        // 转换为 base64 - 避免堆栈溢出
        const uint8Array = new Uint8Array(compressed);
        let binaryString = '';
        const chunkSize = 1024; // 分块处理避免堆栈溢出

        for (let i = 0; i < uint8Array.length; i += chunkSize) {
          const chunk = uint8Array.slice(i, i + chunkSize);
          binaryString += String.fromCharCode.apply(null, Array.from(chunk));
        }

        const base64 = btoa(binaryString);
        const dataUrl = `data:image/png;base64,${base64}`;

        resolve(dataUrl);
      } catch (error) {
        reject(error);
      }
    };

    img.onerror = () => reject(new Error('图片加载失败'));
    img.src = imageUrl;
  });
}
/**
 * 压缩图片到指定质量
 * @param imageUrl 图片URL
 * @param quality 质量参数 (0-1)
 * @param format 输出格式
 * @returns Promise<string> 压缩后的base64数据URL
 */
async function compressImageWithQuality(
  imageUrl: string,
  quality: number,
  format: SupportedFormat = 'jpg'
): Promise<string> {
  // 如果是 PNG 格式，使用 UPNG.js 进行压缩
  if (format === 'png') {
    return compressPngWithUPNG(imageUrl, quality);
  }

  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';

    img.onload = () => {
      try {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        if (!ctx) {
          reject(new Error('无法获取Canvas上下文'));
          return;
        }

        canvas.width = img.width;
        canvas.height = img.height;

        // 绘制图片
        ctx.drawImage(img, 0, 0);

        // 获取MIME类型
        const mimeType = format === 'jpg' ? 'image/jpeg' : `image/${format}`;

        // 转换为指定格式和质量
        const compressedDataUrl = canvas.toDataURL(mimeType, quality);
        resolve(compressedDataUrl);
      } catch (error) {
        reject(error);
      }
    };

    img.onerror = () => reject(new Error('图片加载失败'));
    img.src = imageUrl;
  });
}

/**
 * 二分查找最佳压缩质量以达到目标文件大小（PNG 特殊处理）
 * @param imageUrl 图片URL
 * @param targetSizeBytes 目标文件大小（字节）
 * @param format 输出格式
 * @param maxIterations 最大迭代次数
 * @returns Promise<{dataUrl: string, quality: number}> 压缩结果
 */
async function compressToTargetSize(
  imageUrl: string,
  targetSizeBytes: number,
  format: SupportedFormat = 'jpg',
  maxIterations: number = 10
): Promise<{ dataUrl: string; quality: number }> {
  // PNG 格式使用不同的策略
  if (format === 'png') {
    // PNG 使用预设的质量级别，因为颜色数量不是线性的
    const qualityLevels = [1.0, 0.9, 0.7, 0.5, 0.3];
    let bestResult = { dataUrl: '', quality: 1.0 };
    let bestSize = Infinity;

    for (const quality of qualityLevels) {
      const compressedDataUrl = await compressPngWithUPNG(imageUrl, quality);
      const currentSize = getImageSizeFromDataUrl(compressedDataUrl);

      // 如果当前大小符合目标或更接近目标
      if (
        currentSize <= targetSizeBytes ||
        Math.abs(currentSize - targetSizeBytes) <
          Math.abs(bestSize - targetSizeBytes)
      ) {
        bestResult = { dataUrl: compressedDataUrl, quality };
        bestSize = currentSize;
      }

      // 如果已经达到目标大小，停止
      if (currentSize <= targetSizeBytes) {
        break;
      }
    }

    return bestResult;
  }

  // 对于其他格式使用原有的二分查找逻辑
  let minQuality = 0.1;
  let maxQuality = 1.0;
  let bestResult = { dataUrl: '', quality: 1.0 };

  for (let i = 0; i < maxIterations; i++) {
    const currentQuality = (minQuality + maxQuality) / 2;
    const compressedDataUrl = await compressImageWithQuality(
      imageUrl,
      currentQuality,
      format
    );
    const currentSize = getImageSizeFromDataUrl(compressedDataUrl);

    bestResult = { dataUrl: compressedDataUrl, quality: currentQuality };

    // 如果当前大小接近目标大小（误差在5%以内），则停止
    if (Math.abs(currentSize - targetSizeBytes) / targetSizeBytes < 0.05) {
      break;
    }

    if (currentSize > targetSizeBytes) {
      maxQuality = currentQuality;
    } else {
      minQuality = currentQuality;
    }
  }

  return bestResult;
}

/**
 * 压缩单张图片
 * @param imageUrl 图片URL
 * @param settings 压缩设置
 * @param originalSize 原始文件大小（字节）
 * @param format 输出格式
 * @returns Promise<CompressionResult> 压缩结果
 */
export async function compressImage(
  imageUrl: string,
  settings: CompressionSettings,
  originalSize?: number,
  format: SupportedFormat = 'jpg'
): Promise<CompressionResult> {
  // 如果没有提供原始大小，先获取
  if (!originalSize) {
    originalSize = getImageSizeFromDataUrl(imageUrl);
  }

  let compressedUrl: string;

  if (settings.level === 'original') {
    // 原始质量，直接返回
    compressedUrl = imageUrl;
  } else if (settings.level) {
    // 按压缩程度压缩
    const quality = getQualityByLevel(settings.level);
    compressedUrl = await compressImageWithQuality(imageUrl, quality, format);
  } else if (settings.customSize && settings.customUnit) {
    // 按自定义体积压缩
    const targetSizeBytes =
      settings.customUnit === 'MB'
        ? settings.customSize * 1024 * 1024
        : settings.customSize * 1024;

    const result = await compressToTargetSize(
      imageUrl,
      targetSizeBytes,
      format
    );
    compressedUrl = result.dataUrl;
  } else {
    // 默认中度压缩
    compressedUrl = await compressImageWithQuality(imageUrl, 0.7, format);
  }

  const compressedSize = getImageSizeFromDataUrl(compressedUrl);
  const compressionRatio = originalSize > 0 ? compressedSize / originalSize : 1;

  return {
    compressedUrl,
    originalSize,
    compressedSize,
    compressionRatio,
  };
}

/**
 * 批量压缩图片
 * @param images 图片URL数组
 * @param settings 压缩设置
 * @param onProgress 进度回调
 * @param format 输出格式
 * @returns Promise<CompressionResult[]> 压缩结果数组
 */
export async function compressImagesBatch(
  images: Array<{ url: string; originalSize?: number }>,
  settings: CompressionSettings,
  onProgress?: (current: number, total: number, currentImage?: string) => void,
  format: SupportedFormat = 'jpg'
): Promise<CompressionResult[]> {
  const results: CompressionResult[] = [];
  const total = images.length;

  for (let i = 0; i < images.length; i++) {
    const image = images[i];

    try {
      const result = await compressImage(
        image.url,
        settings,
        image.originalSize,
        format
      );
      results.push(result);

      if (onProgress) {
        onProgress(i + 1, total, image.url);
      }
    } catch (error) {
      console.error(`压缩图片失败: ${image.url}`, error);
      // 压缩失败时保持原图
      results.push({
        compressedUrl: image.url,
        originalSize: image.originalSize || 0,
        compressedSize: image.originalSize || 0,
        compressionRatio: 1,
      });

      if (onProgress) {
        onProgress(i + 1, total, image.url);
      }
    }

    // 添加小延迟避免浏览器阻塞
    if (i < images.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 10));
    }
  }

  return results;
}
