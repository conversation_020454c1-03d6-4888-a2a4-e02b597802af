/**
 * 背景相关的工具函数
 * 处理背景图片分类生成、背景颜色验证等功能
 */

import { BACKGROUND_CATEGORIES_CONFIG } from '@/config/backgroundCategories';

/**
 * 预设图片接口
 */
export interface PresetImage {
  id: string;
  name: string;
  url: string;
}

/**
 * 预设图片分类接口
 */
export interface PresetCategory {
  id: string;
  name: string;
  images: PresetImage[];
}

/**
 * 动态生成预设图片分类
 * @param useChineseName 是否使用中文名称，默认为 false（使用英文）
 * @returns 预设分类数组
 */
export const generatePresetCategories = (
  useChineseName: boolean = false
): PresetCategory[] => {
  return Object.entries(BACKGROUND_CATEGORIES_CONFIG).map(
    ([categoryId, config]) => ({
      id: categoryId,
      name: useChineseName ? config.nameCn : config.nameEn,
      images: Array.from({ length: config.imageCount }, (_, index) => ({
        id: `${categoryId}${index + 1}`,
        name: `${useChineseName ? config.nameCn : config.nameEn}${index + 1}`,
        url: `/apps/images/background/${categoryId}/${index + 1}.png`,
      })),
    })
  );
};
