import React from 'react';

interface IconProps {
  className?: string;
  size?: number;
}

const ExportIcon: React.FC<IconProps> = ({ className, size = 24 }) => (
  <svg
    width={size}
    height={size}
    viewBox='0 0 24 24'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    className={className}
  >
    {/* 文档底部 */}
    <rect
      x='3'
      y='12'
      width='18'
      height='9'
      stroke='currentColor'
      strokeWidth='1.5'
      fill='none'
    />
    {/* 上传箭头 */}
    <path
      d='M12 15V4.5'
      stroke='currentColor'
      strokeWidth='1.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
    {/* 箭头左侧 */}
    <path
      d='M7.5 7.5L12 3'
      stroke='currentColor'
      strokeWidth='1.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
    {/* 箭头右侧 */}
    <path
      d='M16.5 7.5L12 3'
      stroke='currentColor'
      strokeWidth='1.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
  </svg>
);

export default ExportIcon;
