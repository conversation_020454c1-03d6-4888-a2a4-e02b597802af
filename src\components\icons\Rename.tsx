import React from 'react';

interface IconProps {
  className?: string;
  size?: number;
}

const RenameIcon: React.FC<IconProps> = ({ className, size = 24 }) => (
  <svg
    width={size}
    height={size}
    viewBox='0 0 24 24'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    className={className}
  >
    <path
      d='M9 15L13.7796 13.6043L20.5858 6.79814C21.3668 6.01709 21.3668 4.75076 20.5858 3.96971L20.0303 3.4142C19.2492 2.63316 17.9829 2.63316 17.2018 3.41421L10.3957 10.2203L9 15Z'
      strokeWidth='1.5'
      strokeLinejoin='round'
    />
    <path
      d='M12 3H7C4.79086 3 3 4.79086 3 7V17C3 19.2091 4.79086 21 7 21H17C19.2091 21 21 19.209 21 16.9998C21 15.2283 21 13.6141 21 12'
      strokeWidth='1.5'
      strokeLinecap='round'
      strokeLinejoin='round'
    />
  </svg>
);

export default RenameIcon;
