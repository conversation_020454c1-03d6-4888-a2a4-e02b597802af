# Dependencies
node_modules/
.pnp
.pnp.js

# Production builds
.next/
out/
build/
dist/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Package managers
package-lock.json
yarn.lock
pnpm-lock.yaml

# IDE
.vscode/
.idea/

# OS
.DS_Store
Thumbs.db

# Git
.git/
.gitignore

# Coverage
coverage/

# Documentation
*.md

# Config files
*.config.js
*.config.ts
*.config.mjs 