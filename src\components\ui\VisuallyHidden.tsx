import * as React from 'react';
import { cn } from '@/lib/base';

const VisuallyHidden = React.forwardRef<
  HTMLSpanElement,
  React.HTMLAttributes<HTMLSpanElement>
>(({ className, ...props }, ref) => (
  <span
    ref={ref}
    className={cn(
      'absolute border-0 clip-rect-0 h-px -m-px overflow-hidden p-0 relative whitespace-nowrap w-px',
      className
    )}
    style={{
      clip: 'rect(0, 0, 0, 0)',
      clipPath: 'inset(50%)',
      height: '1px',
      margin: '-1px',
      overflow: 'hidden',
      padding: '0',
      position: 'absolute',
      whiteSpace: 'nowrap',
      width: '1px',
    }}
    {...props}
  />
));

VisuallyHidden.displayName = 'VisuallyHidden';

export { VisuallyHidden };
