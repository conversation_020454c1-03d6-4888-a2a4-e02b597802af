# 项目字体系统配置

## 📝 概述

本项目使用 **HarmonyOS Sans** 作为主要字体，通过本地字体文件的方式加载，提供现代化、优雅的中文和英文显示效果。

## 🎯 字体配置

### 主要字体 - HarmonyOS Sans
- **用途**: 全站主要字体，用于所有文本显示
- **来源**: 本地字体文件（从华为官网下载）
- **权重**: 400(Regular), 500(Medium), 700(Bold)
- **文件位置**: `public/fonts/` (作为静态资源)
- **特点**: 
  - 优秀的中文显示效果
  - 现代化的设计风格
  - 良好的可读性
  - 支持多种字重

### 等宽字体 - 系统默认
- **用途**: 代码显示和等宽文本
- **来源**: 系统默认等宽字体族
- **特点**: 使用系统原生等宽字体

## 🚀 实现方式

### 1. 本地字体文件配置 (layout.tsx)
```typescript
// 配置 HarmonyOS Sans 本地字体（静态资源）
const harmonyOSSans = localFont({
  variable: '--font-harmonyos-sans',
  src: [
    {
      path: '../../public/fonts/HarmonyOS_Sans_Regular.ttf',
      weight: '400',
      style: 'normal',
    },
    {
      path: '../../public/fonts/HarmonyOS_Sans_Medium.ttf',
      weight: '500',
      style: 'normal',
    },
    {
      path: '../../public/fonts/HarmonyOS_Sans_Bold.ttf',
      weight: '700',
      style: 'normal',
    },
  ],
  fallback: ['-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'sans-serif'],
  display: 'swap',
});
```

> **注意**: 字体文件放在 `public/fonts/` 目录下作为静态资源，在 `localFont` 中使用相对路径 `../../public/fonts/` 来引用。

### 2. CSS 配置 (globals.css)
```css
/* HarmonyOS Sans 字体现在通过 Next.js localFont 本地加载 */

/* 字体变量定义 */
:root {
  /* 字体配置 - HarmonyOS Sans 通过本地字体文件加载 */
  --font-harmonyos-sans:
    'HarmonyOS Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto',
    'Helvetica Neue', Arial, sans-serif;
  --font-geist-mono:
    'Geist Mono', 'SF Mono', Monaco, 'Inconsolata', 'Fira Code',
    'Droid Sans Mono', 'Source Code Pro', monospace;
}

/* 全局字体应用 */
body {
  font-family: var(--font-harmonyos-sans);
}
```

### 3. Tailwind CSS 集成
```css
@theme inline {
  --font-sans: var(--font-harmonyos-sans);
  --font-mono: var(--font-geist-mono);
}
```

## 📁 文件结构

```
项目根目录/
├── public/fonts/               # 字体文件目录（静态资源）
│   ├── HarmonyOS_Sans_Regular.ttf   # 常规字重
│   ├── HarmonyOS_Sans_Medium.ttf    # 中等字重
│   └── HarmonyOS_Sans_Bold.ttf      # 粗体字重
├── src/app/layout.tsx          # 字体配置入口
└── src/app/globals.css         # CSS 变量定义
```

## 🎨 使用方法

### 在 JSX 中使用
```jsx
// 自动使用 HarmonyOS Sans（默认）
<p className="text-base">这是 HarmonyOS Sans 字体</p>

// 使用指定字重
<h1 className="font-medium">中等字重标题</h1>
<h2 className="font-bold">粗体标题</h2>

// 使用等宽字体
<code className="font-mono">代码文本</code>
```

### 在 CSS 中使用
```css
.custom-text {
  font-family: var(--font-harmonyos-sans);
  font-weight: 500; /* Medium */
}

.code-block {
  font-family: var(--font-geist-mono);
}
```

## ⚡ 性能优化

1. **静态资源加载**: 字体文件作为静态资源放在 `public/` 目录，避免网络请求延迟
2. **字体预加载**: 使用 `display: swap` 避免字体闪烁
3. **回退字体**: 完善的系统字体回退链
4. **按需加载**: 只加载项目实际使用的字重（Regular, Medium, Bold）
5. **浏览器缓存**: 静态资源会被浏览器长期缓存，提升后续访问速度

## 🔧 维护指南

### 添加新字重
1. 下载对应的 HarmonyOS Sans 字体文件
2. 将文件放入 `public/fonts/` 目录（作为静态资源）
3. 在 `layout.tsx` 的 `src` 数组中添加新的配置项

### 更新字体
1. 替换 `public/fonts/` 目录下的字体文件
2. 确保文件名和配置保持一致
3. 重新构建项目验证

## ⚠️ 注意事项

1. **版权**: 确保使用的字体文件符合许可协议
2. **文件大小**: HarmonyOS Sans 字体文件较大（每个约152KB），注意对项目体积的影响
3. **浏览器兼容性**: 现代浏览器都支持 TTF 格式字体
4. **静态资源**: 字体文件作为静态资源，会在构建时被复制到输出目录
5. **缓存策略**: 字体文件会被浏览器长期缓存，更新时可能需要强制刷新
6. **路径引用**: 在 `localFont` 中使用相对路径引用 `public` 目录下的文件

## 🚀 最佳实践

1. **静态资源管理**: 将字体文件放在 `public/fonts/` 目录，便于管理和部署
2. **性能优化**: 优先使用较轻的字重（Regular, Medium），避免加载不必要的字重
3. **可读性**: 正文使用 Regular(400)，标题使用 Medium(500) 或 Bold(700)
4. **一致性**: 整个项目统一使用相同的字体配置，避免内联样式
5. **回退策略**: 始终提供完整的字体回退链，确保在字体加载失败时的显示效果
6. **路径维护**: 使用相对路径引用字体文件，确保在不同环境下都能正确加载

## 📝 注意事项

1. **避免内联样式**: 不要使用 `style={{ fontFamily: '...' }}`，使用 Tailwind 类或 CSS 变量
2. **保持一致性**: 统一使用配置好的字体系统，避免随意指定字体
3. **性能考量**: 避免加载过多不必要的字重和样式
4. **测试验证**: 在不同设备和浏览器上测试字体显示效果

## 🎨 设计指南

### 推荐字重搭配
- **标题**: `font-bold` (700) 或 `font-semibold` (600)
- **正文**: `font-normal` (400) 或 `font-medium` (500)
- **辅助文字**: `font-light` (300) 或 `font-normal` (400)
- **按钮文字**: `font-medium` (500) 或 `font-semibold` (600)

### 可读性优化
- 正文建议使用 400 或 500 字重
- 小字号文本避免使用过细字重
- 深色背景上适当增加字重
- 注意文字与背景的对比度

## 🚀 部署说明

### 生产环境
- 字体文件会随着 `public` 目录被自动复制到构建输出中
- 确保在部署时 `public/fonts/` 目录下的所有字体文件都被正确上传
- 字体文件将以静态资源的形式提供服务

### 构建验证
```bash
# 构建项目
npm run build

# 启动生产服务器
npm run start
```

### CDN 优化（可选）
如果使用 CDN，确保字体文件也被正确缓存和分发：
- 设置合适的缓存头（如 `Cache-Control: public, max-age=31536000`）
- 考虑使用字体文件的版本控制

---

*本文档记录了项目字体系统的完整配置，确保团队成员了解和正确使用字体规范。* 